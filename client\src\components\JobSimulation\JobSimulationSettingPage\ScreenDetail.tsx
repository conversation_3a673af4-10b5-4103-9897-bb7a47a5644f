import { useNavigate, useParams } from 'react-router-dom';
import ImageClick from './ImageClick';

export default function ScreenDetail() {
  const navigate = useNavigate();
  const { screenId } = useParams();

  const handleBackClick = () => {
    navigate('/job-simulation/setting');
  };

  return (
    <>
      <div className="p-8 text-white">
        <div className="mb-6">
          <button
            className="rounded-md border border-gray-300 px-4 py-2 hover:bg-gray-700"
            onClick={handleBackClick}
          >
            ← Back
          </button>
        </div>

        <div className="mb-4 flex items-center justify-between">
          <p className="text-lg font-medium">
            Draw the square into the screen to create the Action
          </p>
          <button className="rounded-md border border-gray-300 px-4 py-1 hover:bg-gray-700">
            Save
          </button>
        </div>

        {/* <div className="mb-6 flex gap-4">
          <button className="rounded-md border border-gray-300 px-6 py-2 hover:bg-gray-700">
            Create Ad ↙
          </button>
          <button className="rounded-md border border-gray-300 px-6 py-2 hover:bg-gray-700">
            Public Ad ↙
          </button>
        </div> */}

        {/* <div onClick={() => setOpenModal(true)}>
          <img src={'/assets/job-simulation/facebook-ads/simple/001.png'} alt="screen" />
        </div> */}

        <ImageClick image={`/assets/job-simulation/facebook-ads/simple/${screenId}.png`} />
      </div>
    </>
  );
}
