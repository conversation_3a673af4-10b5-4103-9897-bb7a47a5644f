
export default function SettingIcon({
  color = '#34A1F4',
  size = '24',
  opacity = '0.2',
}: {
  color?: string;
  size?: string | number;
  opacity?: string | number;
}) {
  return (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g opacity={opacity}>
        <path d="M12.0111 9.15449C11.3103 9.15449 10.6541 9.42637 10.1572 9.92324C9.66268 10.4201 9.38846 11.0764 9.38846 11.7771C9.38846 12.4779 9.66268 13.1342 10.1572 13.6311C10.6541 14.1256 11.3103 14.3998 12.0111 14.3998C12.7119 14.3998 13.3681 14.1256 13.865 13.6311C14.3596 13.1342 14.6338 12.4779 14.6338 11.7771C14.6338 11.0764 14.3596 10.4201 13.865 9.92324C13.6223 9.67869 13.3335 9.4848 13.0152 9.35283C12.697 9.22085 12.3557 9.15344 12.0111 9.15449ZM21.6744 14.674L20.1416 13.3639C20.2142 12.9186 20.2517 12.4639 20.2517 12.0115C20.2517 11.5592 20.2142 11.1021 20.1416 10.6592L21.6744 9.34902C21.7902 9.2499 21.8731 9.11787 21.912 8.97051C21.9509 8.82314 21.9441 8.66741 21.8924 8.52402L21.8713 8.46309C21.4494 7.28345 20.8173 6.19001 20.0056 5.23574L19.9635 5.18652C19.8649 5.07063 19.7335 4.98732 19.5867 4.94757C19.4398 4.90783 19.2844 4.91351 19.1408 4.96387L17.2377 5.64121C16.5346 5.06465 15.7517 4.60996 14.9033 4.29356L14.5353 2.30371C14.5076 2.15381 14.4349 2.0159 14.3269 1.90832C14.2188 1.80073 14.0807 1.72855 13.9307 1.70137L13.8674 1.68965C12.6486 1.46934 11.3642 1.46934 10.1455 1.68965L10.0822 1.70137C9.93221 1.72855 9.79402 1.80073 9.68601 1.90832C9.578 2.0159 9.50529 2.15381 9.47753 2.30371L9.10721 4.30293C8.2668 4.62184 7.48391 5.07543 6.78924 5.6459L4.87206 4.96387C4.72854 4.91311 4.57297 4.90722 4.42603 4.94699C4.27909 4.98676 4.14773 5.07031 4.0494 5.18652L4.00721 5.23574C3.19695 6.19102 2.56502 7.28419 2.14159 8.46309L2.12049 8.52402C2.01503 8.81699 2.10174 9.14512 2.33846 9.34902L3.89003 10.6732C3.81737 11.1139 3.78221 11.5639 3.78221 12.0092C3.78221 12.4592 3.81737 12.9092 3.89003 13.3451L2.34315 14.6693C2.22736 14.7685 2.14449 14.9005 2.10555 15.0479C2.06662 15.1952 2.07346 15.351 2.12518 15.4943L2.14628 15.5553C2.57049 16.7342 3.19628 17.824 4.0119 18.7826L4.05409 18.8318C4.15265 18.9477 4.28402 19.031 4.43087 19.0708C4.57773 19.1105 4.73318 19.1049 4.87674 19.0545L6.79393 18.3725C7.49237 18.9467 8.27049 19.4014 9.1119 19.7154L9.48221 21.7147C9.50998 21.8646 9.58269 22.0025 9.6907 22.11C9.79871 22.2176 9.93689 22.2898 10.0869 22.317L10.1502 22.3287C11.3809 22.5502 12.6413 22.5502 13.8721 22.3287L13.9353 22.317C14.0853 22.2898 14.2235 22.2176 14.3315 22.11C14.4395 22.0025 14.5123 21.8646 14.54 21.7147L14.908 19.7248C15.7564 19.4061 16.5392 18.9537 17.2424 18.3771L19.1455 19.0545C19.289 19.1053 19.4446 19.1111 19.5915 19.0714C19.7385 19.0316 19.8698 18.9481 19.9681 18.8318L20.0103 18.7826C20.826 17.8193 21.4517 16.7342 21.876 15.5553L21.8971 15.4943C21.9978 15.2037 21.9111 14.8779 21.6744 14.674ZM12.0111 15.8975C9.73534 15.8975 7.89081 14.0529 7.89081 11.7771C7.89081 9.50137 9.73534 7.65684 12.0111 7.65684C14.2869 7.65684 16.1314 9.50137 16.1314 11.7771C16.1314 14.0529 14.2869 15.8975 12.0111 15.8975Z" fill={color} />
      </g>
    </svg>

  );
}
