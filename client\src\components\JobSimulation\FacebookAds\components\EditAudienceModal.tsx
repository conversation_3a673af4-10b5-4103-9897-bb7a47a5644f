import React, { useState } from 'react';
import { X, Search, Info, MapPin, Plus, Minus } from 'lucide-react';
import { <PERSON><PERSON>, Dialog, DialogContent, DialogHeader, DialogTitle, DualSlider, Input, RadioGroup, RadioGroupItem, Slider } from '~/components/ui';
import { useToastContext } from '~/Providers';

interface EditAudienceModalProps {
	isOpen: boolean;
	onClose: () => void;
	onSave: (audienceData: any) => void;
	initialData?: {
		gender: string;
		ageRange: number[];
		locations: string[];
		interests: string[];
	};
	isCreatingNew?: boolean;
	currentName?: string;
}

const EditAudienceModal = ({
	isOpen,
	onClose,
	onSave,
	initialData,
	isCreatingNew = false,
	currentName = ''
}: EditAudienceModalProps) => {
	const { showToast } = useToastContext();
	const [isSaving, setIsSaving] = useState(false);
	const [audienceName, setAudienceName] = useState(currentName || `Audience-${new Date().toISOString().split('T')[0]}`);
	const [gender, setGender] = useState(initialData?.gender || 'all');
	const [ageRange, setAgeRange] = useState(initialData?.ageRange || [18, 65]);
	const [showMap, setShowMap] = useState(false);
	const [locationInput, setLocationInput] = useState('');
	const [selectedLocations, setSelectedLocations] = useState<string[]>(initialData?.locations || ['Vietnam']);
	const [suggestions, setSuggestions] = useState<string[]>([]);
	const [interests, setInterests] = useState<string[]>(initialData?.interests || ['Amazon.com (retailer)']);

	const handleLocationInputChange = (value: string) => {
		setLocationInput(value);
		setShowMap(true);

		// Mock suggestions based on input
		if (value.length > 0) {
			const mockSuggestions = [
				'Macedonia',
				'Ame, Debar',
				'Malaysia',
				'Maldives'
			].filter(location =>
				location.toLowerCase().includes(value.toLowerCase())
			);
			setSuggestions(mockSuggestions);
		} else {
			setSuggestions([]);
		}
	};

	const handleLocationSelect = (location: string) => {
		if (!selectedLocations.includes(location)) {
			setSelectedLocations([...selectedLocations, location]);
		}
		setLocationInput('');
		setSuggestions([]);
	};

	const removeLocation = (location: string) => {
		setSelectedLocations(selectedLocations.filter(loc => loc !== location));
	};

	const removeInterest = (interest: string) => {
		setInterests(interests.filter(int => int !== interest));
	};

	const handleSave = async () => {
		setIsSaving(true);
		const audienceData = {
			...(isCreatingNew && { name: audienceName }),
			gender,
			ageRange,
			locations: selectedLocations,
			interests
		};
		setTimeout(() => {
			onSave(audienceData);
			showToast({ message: 'Audience saved successfully!' });
			setIsSaving(false);
			onClose();
		}, 2000);
	};

	return (
		<Dialog open={isOpen} onOpenChange={(open) => {
			if (!isSaving && !open) {
				onClose();
			}
		}}>
			<DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto p-0">
				<DialogHeader>
					<DialogTitle className="flex items-center justify-betwee font-medium px-4 pt-4 text-xl">
						{isCreatingNew ? 'Create new audience' : 'Edit Advantage+ audience'}
					</DialogTitle>
				</DialogHeader>
				<hr />
				<div className="space-y-6 p-4 pt-0">
					{isCreatingNew && (
						<div className="space-y-4">
							<p className="text-sm text-gray-600">
								Select the location, age, gender and interests of <span className="font-medium">Accounts Center accounts</span> you want to reach with your ad.
							</p>

							<div className="space-y-2">
								<h3 className="font-medium">Audience name</h3>
								<Input
									value={audienceName}
									onChange={(e) => setAudienceName(e.target.value)}
									placeholder="Enter audience name"
									className="w-full"
								/>
							</div>
						</div>
					)}

					{!isCreatingNew && (
						<p className="text-sm text-gray-600">
							Details you add here help our system understand the type of people you want to reach. If you set a minimum age below 26 we’ll use that as a requirement along with location. All other criteria will be used as suggestions to help find the best audience for your ad.
						</p>
					)}

					{/* Gender Section */}
					<div className="space-y-3">
						<div className="flex items-center space-x-2">
							<h3 className="font-medium">Gender</h3>
							<Info className="w-4 h-4 text-gray-400" />
						</div>
						<RadioGroup
							value={gender}
							onValueChange={setGender}
							className="grid grid-cols-3 gap-4"
						>
							<label className="flex items-center space-x-2 border rounded-lg p-3 cursor-pointer hover:bg-gray-50">
								<RadioGroupItem value="all" />
								<span>All</span>
							</label>
							<label className="flex items-center space-x-2 border rounded-lg p-3 cursor-pointer hover:bg-gray-50">
								<RadioGroupItem value="men" />
								<span>Men</span>
							</label>
							<label className="flex items-center space-x-2 border rounded-lg p-3 cursor-pointer hover:bg-gray-50">
								<RadioGroupItem value="women" />
								<span>Women</span>
							</label>
						</RadioGroup>
					</div>

					{/* Age Section */}
					<div className="space-y-4">
						<div className="flex items-center space-x-2">
							<h3 className="font-medium">Age</h3>
							<Info className="w-4 h-4 text-gray-400" />
						</div>

						<div className="flex items-center gap-4">
							<span className="text-sm font-medium">{ageRange[0]}</span>
							<DualSlider
								value={ageRange}
								onValueChange={setAgeRange}
								max={65}
								min={18}
								step={1}
								className="w-full"
							/>
							<span className="text-sm font-medium">{ageRange[1] === 65 ? '65+' : ageRange[1]}</span>
						</div>

						<div className="bg-gray-50 p-3 rounded-lg">
							<p className="text-base text-gray-400">
								When using audience targeting such as gender or interests, you can only target people over 18.{' '}
								<span className="text-blue-600 cursor-pointer hover:underline text-sm">Learn more</span>
							</p>
						</div>
					</div>

					{/* Locations Section */}
					<div className="space-y-4 border rounded p-4">
						<div className="space-y-3 relative">
							<div className="flex items-center gap-4">
								<Search className="w-6 h-6 text-gray-400" />
								<div className='w-full'>
									<p className='text-xs text-gray-400'>Locations</p>
									<Input
										placeholder="Type to add more locations"
										value={locationInput}
										onChange={(e) => handleLocationInputChange(e.target.value)}
										onClick={() => setShowMap(true)}
										className="border-none p-0 w-full text-lg font-normal h-8"
									/>
								</div>
							</div>
							{/* Suggestions Dropdown */}
							{suggestions.length > 0 && (
								<div className="absolute top-10 left-0 right-0 bg-white rounded-lg border drop-shadow-xl z-10 mt-1">
									{suggestions.map((suggestion) => (
										<div
											key={suggestion}
											className="p-3 hover:bg-gray-50 cursor-pointer"
											onClick={() => handleLocationSelect(suggestion)}
										>
											<div className="flex items-center space-x-2">
												<MapPin className="w-4 h-4 text-gray-400" />
												<span className="text-sm">{suggestion}</span>
											</div>
										</div>
									))}
								</div>
							)}
							<hr />
							{/* Selected Locations */}
							<div className="mt-3 space-y-2">
								{selectedLocations.map((location) => (
									<div key={location}>
										<span className="text-xs text-gray-500">{location}</span>
										<div className="flex items-center space-x-2 bg-blue-50 px-2 py-1 rounded w-fit cursor-pointer" onClick={() => removeLocation(location)}>
											<span className="text-blue-500 text-base font-medium">
												{location}
											</span>
											<span>
												<X className="w-3 h-3" />
											</span>
										</div>
									</div>
								))}
							</div>
						</div>
					</div>
				</div>

				{/* Footer */}
				<div className="flex justify-end space-x-3 p-4 border-t">
					<Button variant="outline" onClick={onClose} disabled={isSaving}>
						Cancel
					</Button>
					<Button onClick={handleSave} disabled={isSaving}>
						{isSaving ? (
							<span className="flex items-center space-x-2">
								<svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
									<circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
									<path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4l5-5-5-5v4a12 12 0 100 24v-4l-5 5 5 5v-4a8 8 0 01-8-8z" />
								</svg>
								<span>Saving...</span>
							</span>
						) : (
							'Save audience'
						)}
					</Button>
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default EditAudienceModal;