{"name": "@librechat/backend", "version": "v0.7.7", "description": "", "scripts": {"start": "echo 'please run this from the root directory'", "server-dev": "echo 'please run this from the root directory'", "test": "cross-env NODE_ENV=test jest", "b:test": "NODE_ENV=test bun jest", "test:ci": "jest --ci", "add-balance": "node ./add-balance.js", "list-balances": "node ./list-balances.js", "user-stats": "node ./user-stats.js", "create-user": "node ./create-user.js", "invite-user": "node ./invite-user.js", "ban-user": "node ./ban-user.js", "delete-user": "node ./delete-user.js"}, "repository": {"type": "git", "url": "git+https://github.com/danny-a<PERSON>/LibreChat.git"}, "keywords": [], "author": "", "license": "ISC", "_moduleAliases": {"~": "."}, "imports": {"~/*": "./*"}, "bugs": {"url": "https://github.com/danny-a<PERSON>/LibreChat/issues"}, "homepage": "https://librechat.ai", "dependencies": {"@anthropic-ai/sdk": "^0.37.0", "@aws-sdk/client-s3": "^3.758.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@azure/identity": "^4.7.0", "@azure/search-documents": "^12.0.0", "@azure/storage-blob": "^12.26.0", "@google/generative-ai": "^0.23.0", "@googleapis/youtube": "^20.0.0", "@keyv/mongo": "^2.1.8", "@keyv/redis": "^2.8.1", "@langchain/community": "^0.3.34", "@langchain/core": "^0.3.40", "@langchain/google-genai": "^0.1.11", "@langchain/google-vertexai": "^0.2.2", "@langchain/textsplitters": "^0.1.0", "@librechat/agents": "^2.3.95", "@librechat/data-schemas": "*", "@waylaidwanderer/fetch-event-source": "^3.0.1", "axios": "^1.8.2", "bcryptjs": "^2.4.3", "cohere-ai": "^7.9.1", "compression": "^1.7.4", "connect-redis": "^7.1.0", "cookie": "^0.7.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "dedent": "^1.5.3", "dotenv": "^16.0.3", "eventsource": "^3.0.2", "express": "^4.21.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.4.1", "express-session": "^1.18.1", "express-static-gzip": "^2.2.0", "file-type": "^18.7.0", "firebase": "^11.0.2", "googleapis": "^126.0.1", "handlebars": "^4.7.7", "https-proxy-agent": "^7.0.6", "ioredis": "^5.3.2", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.0", "keyv": "^4.5.4", "keyv-file": "^0.2.0", "klona": "^2.0.6", "librechat-data-provider": "*", "librechat-mcp": "*", "lodash": "^4.17.21", "meilisearch": "^0.38.0", "memorystore": "^1.6.7", "mime": "^3.0.0", "module-alias": "^2.2.3", "mongoose": "^8.12.1", "multer": "^1.4.5-lts.1", "nanoid": "^3.3.7", "nodemailer": "^6.9.15", "ollama": "^0.5.0", "openai": "^4.47.1", "openai-chat-tokens": "^0.2.8", "openid-client": "^5.4.2", "passport": "^0.6.0", "passport-apple": "^2.0.2", "passport-discord": "^0.1.4", "passport-facebook": "^3.0.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "passport-headerapikey": "^1.2.2", "passport-jwt": "^4.0.1", "passport-ldapauth": "^3.0.1", "passport-local": "^1.0.0", "rate-limit-redis": "^4.2.0", "sharp": "^0.33.5", "tiktoken": "^1.0.15", "traverse": "^0.6.7", "ua-parser-js": "^1.0.36", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "youtube-transcript": "^1.2.1", "zod": "^3.22.4"}, "devDependencies": {"jest": "^29.7.0", "mongodb-memory-server": "^10.1.3", "nodemon": "^3.0.3", "supertest": "^7.0.0"}}