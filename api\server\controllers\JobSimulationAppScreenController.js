const JobSimulationAppScreenService = require('~/server/services/JobSimulation/JobSimulationAppScreenService.js');

const JobSimulationAppScreenController = {
  async createMany(req, res) {
    try {
      const screens = await JobSimulationAppScreenService.createMany(req);
      res.status(201).json(screens);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res
        .status(500)
        .json({ error: error.message || `Failed to create job simulation app screens` });
    }
  },
  async update(req, res) {
    try {
      const screen = await JobSimulationAppScreenService.update(req);
      if (!screen) {
        return res.status(404).json({ error: 'Job simulation app screen not found' });
      }
      res.json(screen);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res
        .status(500)
        .json({ error: error.message || `Failed to update job simulation app screen` });
    }
  },
  async updateOrder(req, res) {
    try {
      const updatedScreens = await JobSimulationAppScreenService.updateOrder(req.body);
      res.json(updatedScreens);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res
        .status(500)
        .json({ error: error.message || `Failed to update job simulation app screen order` });
    }
  },
  async deleteById(req, res) {
    try {
      const screen = await JobSimulationAppScreenService.deleteById(req.params.id, req.body);
      if (!screen) {
        return res.status(404).json({ error: 'Job simulation app screen not found' });
      }
      res.json(screen);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res
        .status(500)
        .json({ error: error.message || `Failed to delete job simulation app screen` });
    }
  },
  async getByUserAndApp(req, res) {
    const { query } = req;
    try {
      const userId = req.user?.id || query.userId;
      const appId = query.appId;
      if (!userId || !appId) {
        return res.status(400).json({ error: 'User ID and App ID are required' });
      }
      const page = parseInt(query.page, 10) || 1;
      const limit = parseInt(query.limit, 10) || 10;

      const screens = await JobSimulationAppScreenService.getByUserAndApp({
        userId,
        appId,
        page,
        limit,
      });
      res.json(screens);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res
        .status(500)
        .json({ error: error.message || `Failed to retrieve job simulation app screens` });
    }
  },
  async getById(req, res) {
    try {
      const screen = await JobSimulationAppScreenService.getById(req.params.id);
      if (!screen) {
        return res.status(404).json({ error: 'Job simulation app screen not found' });
      }
      res.json(screen);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res
        .status(500)
        .json({ error: error.message || `Failed to retrieve job simulation app screen` });
    }
  },
};

module.exports = JobSimulationAppScreenController;
