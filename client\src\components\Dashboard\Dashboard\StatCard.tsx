import { Star } from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string;
  unit: string;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  changeText?: string;
  isRating?: boolean;
}

const StatCard = ({
  title,
  value,
  unit,
  change,
  changeType,
  changeText,
  isRating,
}: StatCardProps) => {
  const getChangeColor = () => {
    switch (changeType) {
      case 'positive':
        return 'bg-[#03BB06]';
      case 'negative':
        return 'bg-[#FC4B4E]';
      default:
        return '';
    }
  };

  return (
    <div className="">
      <div className="mb-4 flex gap-2 xl:min-h-9 2xl:min-h-0">
        {/* <img src="/assets/fire.png" alt="Fire" /> */}
        <p className="text-xs font-medium text-gray-500">{title}</p>
      </div>
      <div className="flex items-baseline">
        {isRating && <img src="/assets/star.png" alt="Star" className="mr-1" />}
        <span className="text-2xl font-semibold text-primary">{value}</span>
        {unit && <span className="ml-1 text-sm font-light text-gray-500">{unit}</span>}
      </div>
      {change && (
        <>
          <hr className="my-3" />
          <div className="flex items-center">
            <span
              className={`rounded-full px-2 py-1 text-xs font-medium text-white ${getChangeColor()}`}
            >
              {change}
            </span>
            <span className="ml-2 text-xs text-gray-500">{changeText}</span>
          </div>
        </>
      )}
    </div>
  );
};

export default StatCard;
