import { useEffect, useState } from 'react';
import AppModal from './AppModal';
import { useGetJobSimulationApp, useGetJobSimulationApps } from '~/data-provider/JobSimulation';
import { Skeleton, Popover, PopoverContent, PopoverTrigger } from '~/components/ui';
import { useAuthContext } from '~/hooks';
import {
  useCreateJobSimulationApp,
  useDeleleJobSimulationApp,
  useUpdateJobSimulationApp,
} from '~/data-provider/JobSimulation/mutations';
import { useToastContext } from '~/Providers';
import { useRecoilValue } from 'recoil';
import store from '~/store';
import { EditIcon, DeleteIcon } from '~/components/svg';

export default function AppCreation({ setAppSelected, appSelected }) {
  const [openModal, setOpenModal] = useState(false);
  const [type, setType] = useState<'create' | 'edit'>('create');
  const [openDeletePopover, setOpenDeletePopover] = useState<string | null>(null);

  const { isAuthenticated } = useAuthContext();
  const { showToast } = useToastContext();
  const user = useRecoilValue(store.user);

  const {
    data: apps,
    isLoading,
    refetch,
  } = useGetJobSimulationApps(
    {},
    {
      enabled: isAuthenticated,
      onSuccess: (data) => {
        if (data && data.length > 0 && type !== 'edit') {
          setAppSelected(data[0]);
        }
      },
    },
  );

  // const { data: app } = useGetJobSimulationApp(appSelected?._id, '', {
  //   enabled: !!appSelected?._id && isAuthenticated && openModal && type === 'edit',
  // });

  const createApp = useCreateJobSimulationApp({
    onSuccess: (data) => {
      showToast({
        message: 'App created successfully',
        status: 'success',
      });
      setOpenModal(false);
      refetch();
      setAppSelected(data);
    },
    onError: (error) => {
      showToast({
        message: 'Failed to create app',
        status: 'error',
      });
    },
  });

  const updateApp = useUpdateJobSimulationApp({
    onSuccess: (data) => {
      showToast({
        message: 'App updated successfully',
        status: 'success',
      });
      setOpenModal(false);
      refetch();
      setAppSelected(data);
    },
    onError: (error) => {
      showToast({
        message: 'Failed to update app',
        status: 'error',
      });
    },
  });

  const deleleApp = useDeleleJobSimulationApp({
    onSuccess: () => {
      showToast({
        message: 'App deleted successfully',
        status: 'success',
      });
      refetch();
      setOpenDeletePopover(null);
    },
    onError: (error) => {
      showToast({
        message: 'Failed to delete app',
        status: 'error',
      });
    },
  });

  const handleCreateNewApp = () => {
    setType('create');
    setOpenModal(true);
  };

  const handleEditApp = (e, app) => {
    e.stopPropagation();
    setAppSelected(app);
    setType('edit');
    setOpenModal(true);
  };

  const handleRemoveApp = (e, app) => {
    setAppSelected(app);
    e.stopPropagation();
    deleleApp.mutate({ id: app._id, userId: user?.id });
  };

  const handleOk = (type, data) => {
    if (type === 'edit') {
      updateApp.mutate({ id: appSelected._id, userId: user?.id, ...data });
    } else {
      createApp.mutate({ ...data, userId: user?.id, type: 'public' });
    }
  };

  return (
    <div className="flex gap-4 hover:cursor-pointer">
      <div
        className={`flex h-28 w-28 items-center justify-center rounded-xl border border-gray-20 text-center hover:cursor-pointer`}
        onClick={handleCreateNewApp}
      >
        <div>Create new App</div>
      </div>

      {isLoading
        ? [...Array(3)].map((_, index) => <Skeleton key={index} className="h-28 w-28 rounded-xl" />)
        : apps?.map((app, index) => (
            <div
              key={index}
              className={`group relative flex h-28 w-28 flex-col items-center justify-center rounded-xl border ${app._id === appSelected?._id ? 'border-purple-500' : 'border-gray-20'} text-center`}
              onClick={() => setAppSelected(app)}
            >
              <div className="absolute right-1 top-1 flex gap-1 opacity-0 transition-opacity group-hover:opacity-100">
                {/* Edit icon */}
                <button
                  className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 p-1 text-gray-300 hover:bg-gray-700 hover:text-white"
                  onClick={(e) => handleEditApp(e, app)}
                  title="Edit app"
                >
                  <EditIcon />
                </button>

                {/* Remove icon */}
                <Popover
                  open={openDeletePopover === app._id}
                  onOpenChange={(open) => {
                    setOpenDeletePopover(open ? (app._id as string) : null);
                  }}
                >
                  <PopoverTrigger asChild>
                    <button
                      className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 p-1 text-gray-300 hover:bg-red-600 hover:text-white"
                      title="Remove app"
                      onClick={(e) => {
                        e.stopPropagation();
                        setAppSelected(app);
                      }}
                    >
                      <DeleteIcon />
                    </button>
                  </PopoverTrigger>
                  <PopoverContent
                    className="z-50 w-fit rounded-lg border border-gray-200 bg-white p-2 shadow-lg"
                    align="center"
                  >
                    <div className="w-fit">Are you sure you want to delete this app?</div>
                    <div className="mt-2 flex">
                      <button
                        className="mr-2 rounded bg-gray-200 px-3 py-1 text-sm text-gray-700 hover:bg-gray-300"
                        onClick={() => setOpenDeletePopover(null)}
                      >
                        Cancel
                      </button>
                      <button
                        className="rounded bg-red-600 px-3 py-1 text-sm text-white hover:bg-red-700"
                        onClick={(e) => handleRemoveApp(e, app)}
                      >
                        Remove
                      </button>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>

              {app.name}
              <img src={app.icon} alt={app.name} className="h-16 w-16 rounded-full" />
            </div>
          ))}

      {openModal && (
        <AppModal
          open={openModal}
          onClose={() => setOpenModal(false)}
          app={appSelected}
          type={type}
          onOk={handleOk}
          disabled={!isAuthenticated || !user || createApp.isLoading || updateApp.isLoading}
        />
      )}
    </div>
  );
}
