import { useEffect, useState } from 'react';
import { Composed<PERSON><PERSON>, Bar, XAxis, YAxis, ResponsiveContainer } from 'recharts';
import {
	ChartContainer,
	ChartTooltip,
	ChartTooltipContent
} from '~/components';
const chartConfig = {
	clicks: {
		label: 'Clicks',
		color: '#3b82f6',
	},
	cost: {
		label: 'Cost',
		color: '#ef4444',
	},
	avgCpc: {
		label: 'Avg. CPC',
		color: '#10b981',
	},
};

const initialData = [
	{ date: 'Jun 04', clicks: 12, cost: 250, avgCpc: 0.8 },
	{ date: 'Jun 05', clicks: 8, cost: 180, avgCpc: 0.6 },
	{ date: 'Jun 06', clicks: 15, cost: 320, avgCpc: 1.2 },
	{ date: 'Jun 07', clicks: 22, cost: 480, avgCpc: 1.5 },
	{ date: 'Jun 08', clicks: 18, cost: 390, avgCpc: 1.1 },
	{ date: 'Jun 09', clicks: 25, cost: 550, avgCpc: 1.8 },
	{ date: 'Jun 10', clicks: 30, cost: 680, avgCpc: 2.1 },
	{ date: 'Jun 11', clicks: 28, cost: 620, avgCpc: 1.9 },
	{ date: 'Jun 12', clicks: 35, cost: 750, avgCpc: 2.3 },
	{ date: 'Jun 13', clicks: 32, cost: 700, avgCpc: 2.0 },
	{ date: 'Jun 14', clicks: 40, cost: 850, avgCpc: 2.5 },
	{ date: 'Jun 15', clicks: 45, cost: 920, avgCpc: 2.8 },
	{ date: 'Jun 16', clicks: 38, cost: 800, avgCpc: 2.2 },
	{ date: 'Jun 17', clicks: 42, cost: 880, avgCpc: 2.6 },
];

const MetricsBarChart = () => {
	const [chartData, setChartData] = useState(initialData);
	const [clicksPercent, setClicksPercent] = useState(10);
	const [costPercent, setCostPercent] = useState(15);
	const [avgCpcPercent, setAvgCpcPercent] = useState(5);

	useEffect(() => {
		const interval = setInterval(() => {
			setChartData(prevData => {
				const newData = prevData.map(item => {
					const newClicks = item.clicks + Math.floor(Math.random() * 11) + 5;
					const newCost = item.cost + Math.floor(Math.random() * 151) + 50;
					const newAvgCpc = +(item.avgCpc + (Math.random() * 0.3 + 0.1)).toFixed(2);

					return {
						...item,
						clicks: newClicks > item.clicks ? newClicks : item.clicks + 1,
						cost: newCost > item.cost ? newCost : item.cost + 1,
						avgCpc: newAvgCpc > item.avgCpc ? newAvgCpc : +(item.avgCpc + 0.01).toFixed(2),
					};
				});

				// Cập nhật phần trăm riêng biệt
				setClicksPercent(prev => prev + Math.floor(Math.random() * 5) + 2);   // +2 → +6
				setCostPercent(prev => prev + Math.floor(Math.random() * 5) + 3);     // +3 → +7
				setAvgCpcPercent(prev => prev + Math.floor(Math.random() * 3) + 1);   // +1 → +3

				return newData;
			});
		}, 5000);

		return () => clearInterval(interval);
	}, []);

	// Calculate new current total
	const currentClicks = chartData.reduce((sum, item) => sum + item.clicks, 0);
	const currentCost = chartData.reduce((sum, item) => sum + item.cost, 0);
	const currentAvgCpc = chartData.reduce((sum, d) => sum + d.avgCpc, 0) / chartData.length;

	return (
		<div className="w-full bg-white p-4">
			{/* Metrics Header */}
			<div className="flex mb-8 gap-20">
				{/* Clicks */}
				<div className="flex flex-col">
					<div className="flex items-center space-x-2 mb-2">
						<div className="xxl:w-3 xxl:h-3 h-2 w-2 rounded-full bg-blue-500"></div>
						<span className="text-xs xxl:text-sm font-medium text-gray-600">Clicks</span>
					</div>
					<div className="text-xl xxl:text-3xl font-bold text-gray-900 mb-1">
						+{clicksPercent}%
					</div>
					<div className="text-xs xxl:text-sm text-gray-500">Total: {currentClicks.toLocaleString()}</div>
				</div>

				{/* Cost */}
				<div className="flex flex-col">
					<div className="flex items-center space-x-2 mb-2">
						<div className="xxl:w-3 xxl:h-3 h-2 w-2 rounded-full bg-red-500"></div>
						<span className="text-xs xxl:text-sm font-medium text-gray-600">Cost</span>
					</div>
					<div className="text-xl xxl:text-3xl font-bold text-gray-900 mb-1">
						+{costPercent}%
					</div>
					<div className="text-xs xxl:text-sm text-gray-500">Total: ₫{currentCost.toLocaleString()}</div>
				</div>

				{/* Avg. CPC */}
				<div className="flex flex-col">
					<div className="flex items-center space-x-2 mb-2">
						<div className="xxl:w-3 xxl:h-3 h-2 w-2 rounded-full bg-green-500"></div>
						<span className="text-xs xxl:text-sm font-medium text-gray-600">Avg. CPC</span>
					</div>
					<div className="text-xl xxl:text-3xl font-bold text-gray-900 mb-1">
						+{avgCpcPercent}%
					</div>
					<div className="text-xs xxl:text-sm text-gray-500">Total: ₫{currentAvgCpc.toFixed(1)}</div>
				</div>
			</div>

			{/* Chart */}
			<div className="h-80 w-full overflow-hidden">
				<ChartContainer config={chartConfig} className="w-full h-full">
					<ResponsiveContainer width="100%" height="100%">
						<ComposedChart
							data={chartData}
							margin={{ top: 20, right: 20, left: 20, bottom: 40 }}
							barCategoryGap="15%"
						>
							<XAxis
								dataKey="date"
								axisLine={false}
								tickLine={false}
								tick={{ fontSize: 11, fill: '#6b7280' }}
								interval={0}
								angle={-45}
								textAnchor="end"
								height={60}
							/>
							<YAxis
								yAxisId="left"
								axisLine={false}
								tickLine={false}
								tick={{ fontSize: 11, fill: '#6b7280' }}
								width={60}
							/>
							<YAxis
								yAxisId="right"
								orientation="right"
								axisLine={false}
								tickLine={false}
								tick={{ fontSize: 11, fill: '#6b7280' }}
								width={60}
							/>
							<ChartTooltip content={<ChartTooltipContent />} />
							<Bar
								yAxisId="left"
								dataKey="clicks"
								fill="#3b82f6"
								radius={[2, 2, 0, 0]}
								maxBarSize={15}
							/>
							<Bar
								yAxisId="right"
								dataKey="cost"
								fill="#ef4444"
								radius={[2, 2, 0, 0]}
								maxBarSize={15}
							/>
							<Bar
								yAxisId="left"
								dataKey="avgCpc"
								fill="#10b981"
								radius={[2, 2, 0, 0]}
								maxBarSize={15}
							/>
						</ComposedChart>
					</ResponsiveContainer>
				</ChartContainer>
			</div>
		</div>
	);
};

export default MetricsBarChart;