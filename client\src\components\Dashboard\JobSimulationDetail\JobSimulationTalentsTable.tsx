import { ChevronLeft, ChevronRight, Eye, Search } from "lucide-react";
import { useState } from "react";
import { EyeIcon } from "~/components/svg";
import { Button, Input } from "~/components/ui";
import { cn } from "~/utils";

const JobSimulationTalentsTable = ({ selectedJob }: { selectedJob: "esg-analyst" | "digital-marketing" }) => {
	const [activeTab, setActiveTab] = useState<"talent" | "feedback">("talent");

	const talentsData = {
		"esg-analyst": [
			{ rank: 1, name: "<PERSON>", task: "2/2", score: 97, time: "2 hours", medal: "🥇" },
			{ rank: 2, name: "<PERSON>", task: "2/2", score: 95, time: "2.3 hours", medal: "🥈" },
			{ rank: 3, name: "<PERSON>", task: "2/2", score: 93, time: "3 hours", medal: "🥉" },
			{ rank: 4, name: "<PERSON>", task: "2/2", score: 91, time: "2.1 hours", medal: "4" },
			{ rank: 5, name: "<PERSON>", task: "2/2", score: 90, time: "3 hours", medal: "5" },
			{ rank: 6, name: "<PERSON>", task: "2/2", score: 89, time: "3 hours", medal: "6" },
			{ rank: 7, name: "<PERSON> Nguyen", task: "2/2", score: 88, time: "3 hours", medal: "7" },
			{ rank: 8, name: "<PERSON> McKinney", task: "2/2", score: 82, time: "3 hours", medal: "8" },
			{ rank: 9, name: "Darlene <PERSON>", task: "2/2", score: 72, time: "3 hours", medal: "9" },
			{ rank: 10, name: "Leslie Alexander", task: "1/2", score: 50, time: "3 hours", medal: "10" },
		],
		"digital-marketing": [
			{ rank: 1, name: "Alice Johnson", task: "3/3", score: 98, time: "1.5 hours", medal: "🥇" },
			{ rank: 2, name: "Bob Smith", task: "3/3", score: 96, time: "1.8 hours", medal: "🥈" },
			{ rank: 3, name: "Charlie Brown", task: "3/3", score: 94, time: "2 hours", medal: "🥉" },
			{ rank: 4, name: "Diana Prince", task: "3/3", score: 92, time: "2.2 hours", medal: "4" },
			{ rank: 5, name: "Ethan Hunt", task: "3/3", score: 90, time: "2.5 hours", medal: "5" },
			{ rank: 6, name: "Fiona Gallagher", task: "3/3", score: 88, time: "2.7 hours", medal: "6" },
			{ rank: 7, name: "George Lucas", task: "3/3", score: 85, time: "3 hours", medal: "7" },
			{ rank: 8, name: "Hannah Baker", task: "3/3", score: 80, time: "3.2 hours", medal: "8" },
			{ rank: 9, name: "Ian Somerhalder", task: "2/3", score: 75, time: "3.5 hours", medal: "9" },
			{ rank: 10, name: "Jane Doe", task: "2/3", score: 60, time: "4 hours", medal: "10" },
		],
	};
	const talents = talentsData[selectedJob];

	return (
		<div className="mt-6">
			<div className="flex space-x-6 mb-6">
				<button
					onClick={() => setActiveTab("talent")}
					className={cn(
						"pb-2",
						activeTab === "talent" ? "border-b-2 border-[#000] font-medium" : ""
					)}
				>
					Top talents
				</button>
				<button
					onClick={() => setActiveTab("feedback")}
					className={cn(
						"pb-2",
						activeTab === "feedback" ? "border-b-2 border-[#000] font-medium" : ""
					)}
				>
					Feedback
				</button>
			</div>

			<div className="relative mb-6">
				<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#505050] h-4 w-4" />
				<Input
					placeholder="Search name"
					className="pl-10 bg-[#F7F7F7] border-none rounded-full max-w-md"
				/>
			</div>
			{/* Block table */}
			{activeTab === "talent" && (
				<div className="overflow-x-auto">
					<table className="w-full">
						<thead>
							<tr className="border-b border-gray-200 text-sm text-[#505050] opacity-60">
								<th className="text-left py-3 px-4 font-light">#</th>
								<th className="text-left py-3 px-4 font-light">Candidate Name</th>
								<th className="text-left py-3 px-4 font-light">Task</th>
								<th className="text-left py-3 px-4 font-light">Completion Time</th>
							</tr>
						</thead>
						<tbody className="divide-y divide-gray-100">
							{talents.map((talent) => (
								<tr key={talent.rank} className="hover:bg-gray-50">
									<td className="py-4 px-4 text-[#505050]">
										<span className="text-lg">{talent.medal}</span>
									</td>
									<td className="py-4 px-4">
										<div>
											{/* <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full mr-3"></div> */}
											<p className="font-medium">{talent.name}</p>
											<p className="font-light text-xs"><EMAIL></p>
										</div>
									</td>
									<td className="py-4 px-4 text-[#505050]">
										<span>{talent.task}</span>
									</td>
									<td className="py-4 px-4 text-[#505050]">
										<span>{talent.time}</span>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			)}
			{activeTab === "feedback" && (
				<div className="text-center py-16 text-gray-400 font-medium">Coming soon...</div>
			)}

			<div className="flex items-center justify-between mt-6">
				<div className="flex items-center space-x-2">
					<span className="text-sm text-gray-500">Rows per page</span>
					<select className="border border-gray-300 rounded px-2 py-1 text-sm w-[60px]">
						<option>10</option>
						<option>25</option>
						<option>50</option>
					</select>
				</div>
				<div className="flex items-center space-x-2">
					<Button size="sm" className="bg-transparent hover:bg-gray-50">
						<ChevronLeft color="black" />
					</Button>
					<Button variant="outline" size="sm" className="flex-1">
						{/* className="bg-[#34A1F4]" */}
						1
					</Button>
					<Button variant="outline" size="sm" className="flex-1">
						2
					</Button>
					<Button size="sm" className="bg-transparent hover:bg-gray-50">
						<ChevronRight color="black" />
					</Button>
				</div>
			</div>
		</div>
	);
};

export default JobSimulationTalentsTable;