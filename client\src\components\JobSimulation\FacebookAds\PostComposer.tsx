import React, { useState } from 'react';
import { Camera, ChevronDown, ChevronLeft } from 'lucide-react';

import { Button, Switch } from '~/components/ui';
import MediaUpload from './components/MediaUpload';
import PostPreview from './components/PostPreview';
import PublishSuccessModal from './components/PublishSuccessModal';
// import ScheduleOptions from './components/ScheduleOptions';
// import SharingFrequencySelector from './components/SharingFrequencySelector';
// import PrivacySettings from './components/PrivacySettings';
// import AudienceSelector from './components/AudienceSelector';

const logo = 'https://media.licdn.com/dms/image/v2/D560BAQFYscrpgaZGDA/company-logo_200_200/company-logo_200_200/0/1698601586093/brightwave_io_logo?e=2147483647&v=beta&t=Yj9ejUkTYxxOxuMxI9s6hqnDMbOihqTadQb0p-C7TOQ'

const PostComposer = ({ onBackAdList, onGoToAdDetail }) => {
	const [postText, setPostText] = useState('');
	const [selectedImages, setSelectedImages] = useState<File[]>([]);
	const [selectedAudience, setSelectedAudience] = useState('Brightwave');
	// const [scheduleMode, setScheduleMode] = useState<'now' | 'schedule' | 'draft'>('now');
	const [showAudienceDropdown, setShowAudienceDropdown] = useState(false);
	// const [shareToStory, setShareToStory] = useState(false);
	const [boostEnabled, setBoostEnabled] = useState(false);
	const [isPublishModalOpen, setIsPublishModalOpen] = useState(false);

	const handleImageUpload = (files: File[]) => {
		setSelectedImages(prev => [...prev, ...files]);
	};

	const removeImage = (index: number) => {
		setSelectedImages(prev => prev.filter((_, i) => i !== index));
	};

	const handlePublish = () => {
		setIsPublishModalOpen(true);
	};

	// const handleSharingFrequencyChange = (value: string) => {
	// 	console.log('Selected sharing frequency:', value);
	// };

	return (
		<div>
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Left Panel - Post Creation */}
				<div>
					<h2 className="text-lg font-medium mb-6 flex items-center gap-3"><span className='cursor-pointer' onClick={onBackAdList}><ChevronLeft /></span> Create post</h2>
					<div className="space-y-6">
						{/* Audience Selection */}
						<div className="relative bg-white drop-shadow-xl rounded p-4">
							<p className="text-base font-medium mb-2">
								Post to
							</p>
							<div className="relative">
								<button
									onClick={() => setShowAudienceDropdown(!showAudienceDropdown)}
									className="w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-lg bg-white hover:bg-gray-50"
								>
									<div className="flex items-center space-x-2">
										<div className="w-8 h-8 rounded-full flex items-center justify-center">
											<img src={logo} width="100%" height="100%" className='w-full h-full rounded-full' alt='Brightwave Logo' />
										</div>
										<span className="text-sm text-gray-700">{selectedAudience}</span>
									</div>
									<ChevronDown className="w-4 h-4 text-gray-400" />
								</button>

								{/* {showAudienceDropdown && (
									<AudienceSelector
										selectedAudience={selectedAudience}
										onSelect={(audience) => {
											setSelectedAudience(audience);
											setShowAudienceDropdown(false);
										}}
										onClose={() => setShowAudienceDropdown(false)}
									/>
								)} */}
							</div>
						</div>

						<MediaUpload
							selectedImages={selectedImages}
							// showDropShadow={showAudienceDropdown}
							onImageUpload={handleImageUpload}
							onRemoveImage={removeImage}
						/>

						{/* Post Content */}
						<div className='bg-white drop-shadow-xl rounded p-4'>
							<p className="text-base font-medium mb-4">
								Post Details
							</p>
							<div className="relative">
								<p className='text-sm font-medium mb-2'>Text</p>
								<textarea
									value={postText}
									onChange={(e) => setPostText(e.target.value)}
									placeholder="What's on your mind?"
									className="text-sm w-full h-32 p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
								/>
								{/* <div className="absolute bottom-3 right-3 flex space-x-2">
									<button className="p-1 text-gray-400 hover:text-blue-500">
										<Camera className="w-5 h-5" />
									</button>
									<button className="p-1 text-gray-400 hover:text-blue-500">
										<span className="text-sm">#</span>
									</button>
								</div> */}
							</div>
							{/* <div className="flex items-center mt-2 space-x-4 text-gray-400">
								<button className="hover:text-blue-500">
									<span className="text-lg">😊</span>
								</button>
								<button className="hover:text-blue-500">
									<span className="text-lg">📍</span>
								</button>
								<button className="hover:text-blue-500">
									<span className="text-lg">🎵</span>
								</button>
								<button className="hover:text-blue-500">
									<span className="text-lg">🔗</span>
								</button>
								<button className="hover:text-blue-500">
									<span className="text-lg">💬</span>
								</button>
							</div> */}
						</div>

						{/* Schedule Options */}
						{/* <ScheduleOptions
							selectedMode={scheduleMode}
							onModeChange={setScheduleMode}
						/> */}

						{/* Privacy Settings */}
						{/* <div className="bg-white drop-shadow-xl rounded p-4">
							<div className="flex items-start justify-between">
								<p className="text-base font-medium">Share to your story</p>
								<Switch checked={shareToStory} onCheckedChange={setShareToStory} />
							</div>
							<p className="text-sm font-normal mb-4">This is for Facebook only. Your story privacy is set to Public. Anyone on Facebook can see your story.</p>
							<SharingFrequencySelector
								onValueChange={handleSharingFrequencyChange}
							/>
						</div> */}

						{/* Privacy Controls */}
						{/* <PrivacySettings /> */}

						<div className="bg-white drop-shadow-xl rounded p-4">
							<div className="flex items-center justify-between">
								<div className="flex items-center space-x-4">
									<Switch
										checked={boostEnabled}
										onCheckedChange={setBoostEnabled}
										className="data-[state=checked]:bg-blue-600"
									/>
									<p className="text-base font-normal">
										Boost
									</p>
								</div>
								<div className="flex justify-end space-x-3">
									<Button
										variant="outline"
										className="border-gray-300 hover:bg-gray-50 rounded"
									>
										Cancel
									</Button>
									<Button
										className="bg-blue-600 hover:bg-blue-700 text-white rounded"
										onClick={handlePublish}
									>
										Publish
									</Button>
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* Right Panel - Preview */}
				<div className='mx-auto'>
					<div className='w-[500px]'>
						<PostPreview
							logo={logo}
							postText={postText}
							selectedImages={selectedImages}
							selectedAudience={selectedAudience}
						/>
					</div>
				</div>
			</div>
			<PublishSuccessModal
				isOpen={isPublishModalOpen}
				onClose={() => setIsPublishModalOpen(false)}
				id={Date.now()}
				postText={postText}
				selectedImages={selectedImages.map(file => ({
					name: file.name,
					type: file.type,
					size: file.size,
					preview: URL.createObjectURL(file),
				}))}
				selectedAudience={selectedAudience}
				boostEnabled={boostEnabled}
				logo={logo}
				onGoToAdDetail={onGoToAdDetail}
			/>
		</div>
	);
};

export default PostComposer;