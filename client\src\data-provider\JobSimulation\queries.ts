import type { UseQueryOptions, UseQueryResult } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import type t from 'librechat-data-provider';
import { QueryKeys, dataService } from 'librechat-data-provider';

export const useGetJobSimulationDataQuery = (
  jobSimulationId: string,
  _where?: string,
  config?: UseQueryOptions<t.TJobSimulationDataResponse | null>,
): UseQueryResult<t.TJobSimulationDataResponse | null> => {
  return useQuery<t.TJobSimulationDataResponse | null>(
    [QueryKeys.jobSimulationData, jobSimulationId],
    () => {
      console.log('Get Job Simulation Data ::: ', _where);
      return dataService.getJobSimulationData(jobSimulationId);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,
      retry: false,
      enabled: !!jobSimulationId,
      ...config,
    },
  );
};

export const useUserJobSimulationsInfo = (
  params: { page?: number; limit?: number; search?: string },
  config?: UseQueryOptions<any>,
): UseQueryResult<any> => {
  return useQuery<any>(
    [QueryKeys.jobSimulationsUser, params?.page],
    () => {
      return dataService.listUserJobSimulations(params);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,
      retry: 2,
      enabled: true,
      ...config,
    },
  );
};

// export const useUserJobSimulationsInfo = (
//   params?: { page?: string; limit?: string; search?: string },
//   options?: any,
// ) => {
//   return useQuery({
//     queryKey: [QueryKeys.jobSimulationsUser, params],
//     queryFn: () => dataService.listUserJobSimulations(params || {}),
//     ...(options || {}),
//   });
// };

// For admin
export const useAdminJobSimulationList = (
  params?: { page?: string; limit?: string; search?: string },
  options?: any,
) => {
  return useQuery({
    queryKey: ['adminJobSimulations', params],
    queryFn: () => dataService.listJobSimulations(params || {}),
    ...(options || {}),
  });
};

export const useAdminJobSimulationDetail = (jobSimulationId: string, options?: any) => {
  return useQuery({
    queryKey: ['adminJobSimulationDetail', jobSimulationId],
    queryFn: () => dataService.getJobSimulationDetail(jobSimulationId),
    enabled: !!jobSimulationId,
    ...(options || {}),
  });
};

// query for job simulation app
export const useGetJobSimulationApps = (
  params?: { page?: number; limit?: number },
  config?: UseQueryOptions<any>,
): UseQueryResult<t.TJobSimulationApp[]> => {
  return useQuery<t.TJobSimulationApp[]>(
    [QueryKeys.getJobSimulationApps, params?.page],
    () => {
      return dataService.listJobSimulationApps();
    },
    {
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,
      retry: 2,
      enabled: true,
      cacheTime: 1000 * 60 * 5,
      ...config,
    },
  );
};

export const useGetJobSimulationApp = (
  appId: string,
  _where?: string,
  config?: UseQueryOptions<any>,
): UseQueryResult<t.TJobSimulationApp | null> => {
  return useQuery<t.TJobSimulationApp | null>(
    [QueryKeys.getJobSimulationApp, appId],
    () => {
      return dataService.getJobSimulationAppById(appId);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,
      retry: false,
      enabled: !!appId,
      ...config,
    },
  );
};
