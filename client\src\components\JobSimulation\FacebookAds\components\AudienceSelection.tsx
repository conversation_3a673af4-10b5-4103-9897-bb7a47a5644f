import React, { useEffect, useState } from 'react';
import { ChevronUp, Edit, Info } from 'lucide-react';
import { Button, RadioGroup, RadioGroupItem } from '~/components/ui';
import EditAudienceModal from './EditAudienceModal';

interface AudienceSelectionProps {
	onCreateNew?: () => void;
	onLearnMore?: () => void;
	audienceInfo: any;
	setAudienceInfo: any;
}

const AudienceSelection = ({ onCreateNew, onLearnMore, audienceInfo, setAudienceInfo }: AudienceSelectionProps) => {
	console.log("🚀 ~ AudienceSelection ~ audienceInfo:", audienceInfo)
	const [selectedOption, setSelectedOption] = useState('advantage');

	const [audienceData, setAudienceData] = useState({
		gender: audienceInfo?.gender,
		ageRange: audienceInfo?.ageRange,
		locations: audienceInfo?.locations,
	});
	const [isEditModalOpen, setIsEditModalOpen] = useState(false);
	const [isCreatingNew, setIsCreatingNew] = useState(false);

	const [customAudiences, setCustomAudiences] = useState<Array<{
		id: string;
		name: string;
		data: any;
	}>>([]);

	const handleEditClick = () => {
		setIsCreatingNew(false);
		setIsEditModalOpen(true);
	};

	const handleCreateNewClick = () => {
		setIsCreatingNew(true);
		setIsEditModalOpen(true);
		if (onCreateNew) onCreateNew();
	};

	const handleSaveAudience = (newAudienceData: any) => {
		console.log('Audience data saved:', newAudienceData);

		if (isCreatingNew && newAudienceData.name) {
			// Create new custom audience
			const newCustomAudience = {
				id: `custom-${Date.now()}`,
				name: newAudienceData.name,
				data: {
					gender: newAudienceData.gender,
					ageRange: newAudienceData.ageRange,
					locations: newAudienceData.locations,
				}
			};
			setCustomAudiences([...customAudiences, newCustomAudience]);
			setSelectedOption(newCustomAudience.id);
		} else {
			setAudienceData({
				gender: newAudienceData.gender,
				ageRange: newAudienceData.ageRange,
				locations: newAudienceData.locations,
			});
		}
	};

	const getGenderDisplay = (data = audienceData) => {
		switch (data.gender) {
			case 'men': return 'Men';
			case 'women': return 'Women';
			default: return 'All';
		}
	};

	const getAgeDisplay = (data = audienceData) => {
		const [min, max] = data.ageRange;
		return max === 65 ? `${min} - 65+` : `${min} - ${max}`;
	};

	const getCurrentAudienceData = () => {
		if (selectedOption === 'advantage' || selectedOption === 'targeting') {
			return audienceData;
		}
		const customAudience = customAudiences.find(aud => aud.id === selectedOption);
		return customAudience?.data || audienceData;
	};

	const getCurrentAudienceName = () => {
		const customAudience = customAudiences.find(aud => aud.id === selectedOption);
		return customAudience?.name;
	};

	useEffect(() => {
		const current = {
			selectedOption,
			...audienceData,
		};
		if (JSON.stringify(audienceInfo) !== JSON.stringify(current)) {
			setAudienceInfo(current);
		}
	}, [selectedOption, audienceData]);
	console.log(getCurrentAudienceData().locations.join(', '))
	return (
		<div className="bg-white rounded-lg drop-shadow-lg p-4">
			{/* Header */}
			<div className="mb-4">
				<div className='flex items-center justify-between'>
					<p className="text-lg font-medium">Audience</p>
					<Info className="w-5 h-5 text-gray-400" />
				</div>
				<p className="text-sm">Who should see your ad?</p>
			</div>

			{/* Radio Group */}
			<RadioGroup
				value={selectedOption}
				onValueChange={setSelectedOption}
				className="space-y-4"
			>
				{/* Advantage+ audience option */}
				<div>
					<label className="flex items-start space-x-4 cursor-pointer">
						<RadioGroupItem
							value="advantage"
							className="mt-1 flex-shrink-0"
						/>
						<div className="flex-1">
							<div className="text-base font-medium">
								Advantage+ audience
							</div>
							<div className="text-sm text-gray-400">
								Let our ad technology automatically find your audience and adjust over time to reach more people who are likely to respond to your ad.{' '}
								<button className="text-gray-700 hover:text-gray-800 underline">
									Learn more
								</button>
							</div>

							{/* Show advantage+ details when selected */}

						</div>
					</label>
					{selectedOption === 'advantage' && (
						<div className="bg-gray-50 rounded-lg p-4 space-y-3 mt-4">
							<div className="flex items-center justify-between">
								<p className="text-base">Audience details</p>
								<Edit
									className="w-4 h-4 text-gray-600 cursor-pointer hover:text-gray-800"
									onClick={handleEditClick}
								/>
							</div>
							<div className="space-y-2 text-sm">
								<div>
									<span className="text-gray-400">Location: </span>
									<span className="text-gray-900">{getCurrentAudienceData().locations.join(', ')}</span>
								</div>
								<div>
									<span className="text-gray-400">Gender: </span>
									<span className="text-gray-900">{getGenderDisplay(getCurrentAudienceData())}</span>
								</div>
								<div>
									<span className="text-gray-400">Minimum age: </span>
									<span className="text-gray-900">{getAgeDisplay(getCurrentAudienceData())}</span>
								</div>
								<div>
									<span className="text-gray-400">Advantage+ audience: </span>
									<span className="text-gray-900">On</span>
								</div>
							</div>
						</div>
					)}
				</div>
				<div>
					{/* People you choose through targeting option */}
					<label className="flex items-start space-x-4 cursor-pointer">
						<RadioGroupItem
							value="targeting"
							className="mt-1 flex-shrink-0"
						/>
						<div className="flex-1">
							<div className="text-lg font-medium text-gray-900 mb-2">
								People you choose through targeting
							</div>
						</div>
					</label>
					{/* Show targeting details when selected */}
					{selectedOption === 'targeting' && (
						<div className="space-y-4">
							<div className="bg-gray-50 rounded-lg p-4 space-y-3">
								<div className="flex items-center justify-between">
									<h4 className="text-base">Audience details</h4>
									<Edit
										className="w-4 h-4 text-gray-600 cursor-pointer hover:text-gray-800"
										onClick={handleEditClick}
									/>
								</div>
								<div className="space-y-2 text-sm">
									<div>
										<span className="text-gray-400">Location: </span>
										<span className="text-gray-900">{getCurrentAudienceData().locations?.join(', ')}</span>
									</div>
									<div>
										<span className="text-gray-400">Gender: </span>
										<span className="text-gray-900">{getGenderDisplay(getCurrentAudienceData())}</span>
									</div>
									<div>
										<span className="text-gray-400">Minimum age: </span>
										<span className="text-gray-900">{getAgeDisplay(getCurrentAudienceData())}</span>
									</div>
									<div>
										<span className="text-gray-400">Advantage+ audience: </span>
										<span className="text-gray-900">Off</span>
									</div>
								</div>
							</div>
						</div>
					)}
				</div>
				{/* Custom audiences */}
				{customAudiences.map((audience) => (
					<label key={audience.id} className="flex items-start space-x-4 cursor-pointer">
						<RadioGroupItem
							value={audience.id}
							className="mt-1 flex-shrink-0"
						/>
						<div className="flex-1">
							<div className="text-lg font-medium text-gray-900 mb-2">
								{audience.name}
							</div>

							{/* Show custom audience details when selected */}
							{selectedOption === audience.id && (
								<div className="bg-gray-50 rounded-lg p-4 space-y-3">
									<div className="flex items-center justify-between">
										<h4 className="text-lg font-medium text-gray-900">Audience details</h4>
										<Edit
											className="w-4 h-4 text-gray-600 cursor-pointer hover:text-gray-800"
											onClick={handleEditClick}
										/>
									</div>
									<div className="space-y-2 text-sm">
										<div>
											<span className="text-gray-600">Location: </span>
											<span className="text-gray-900">{audience.data.locations.join(', ')}</span>
										</div>
										<div>
											<span className="text-gray-600">Gender: </span>
											<span className="text-gray-900">{getGenderDisplay(audience.data)}</span>
										</div>
										<div>
											<span className="text-gray-600">Age: </span>
											<span className="text-gray-900">{getAgeDisplay(audience.data)}</span>
										</div>
										{audience.data.interests.length > 0 && (
											<div>
												<span className="text-gray-600">Interests: </span>
												<span className="text-gray-900">{audience.data.interests.join(', ')}</span>
											</div>
										)}
									</div>
								</div>
							)}
						</div>
					</label>
				))}
			</RadioGroup>
			{/* Create new button */}
			<div className="pt-4">
				<Button
					variant="outline"
					className="w-full py-3 font-normal"
					onClick={handleCreateNewClick}
				>
					Create new
				</Button>
			</div>
			<EditAudienceModal
				isOpen={isEditModalOpen}
				onClose={() => setIsEditModalOpen(false)}
				onSave={handleSaveAudience}
				isCreatingNew={isCreatingNew}
				currentName={getCurrentAudienceName()}
			/>
		</div>
	);
};

export default AudienceSelection;