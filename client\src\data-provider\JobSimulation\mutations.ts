import type { UseMutationResult } from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type * as t from 'librechat-data-provider';
import { MutationKeys, dataService } from 'librechat-data-provider';

export const useSaveUserInteraction = (
  options?: t.MutationOptions<
    any,
    { jobSimulationId: string; jobSimulationEmail: string; interaction: any },
    unknown,
    unknown
  >,
): UseMutationResult<
  any,
  unknown,
  { jobSimulationId: string; jobSimulationEmail: string; interaction: any },
  unknown
> => {
  return useMutation([MutationKeys.saveJobSimulationUserInteraction], {
    mutationFn: (payload: {
      jobSimulationId: string;
      jobSimulationEmail: string;
      interaction: any;
    }) => dataService.saveJobSimulationUserInteraction(payload),
    ...(options || {}),
    onMutate: (vars) => {
      options?.onMutate?.(vars);
    },
    onSuccess: (...args) => {
      options?.onSuccess?.(...args);
    },
  });
};

export const useDeleteUserInteraction = (
  options?: t.MutationOptions<
    any,
    { jobSimulationId: string; jobSimulationEmail: string },
    unknown,
    unknown
  >,
): UseMutationResult<
  any,
  unknown,
  { jobSimulationId: string; jobSimulationEmail: string },
  unknown
> => {
  return useMutation([MutationKeys.saveJobSimulationUserInteraction], {
    mutationFn: (payload: { jobSimulationId: string; jobSimulationEmail: string }) =>
      dataService.deleteJobSimulationUserInteraction(payload),
    ...(options || {}),
    onMutate: (vars) => {
      options?.onMutate?.(vars);
    },
    onSuccess: (...args) => {
      options?.onSuccess?.(...args);
    },
  });
};

export const useUpdateJobSimulationLogoMutation = (
  options?: t.MutationOptions<t.TJobSimulationDataResponse, FormData, unknown, unknown>,
): UseMutationResult<t.TJobSimulationDataResponse, unknown, FormData, unknown> => {
  return useMutation({
    mutationFn: (formData: FormData) => dataService.updateJobSimulationLogo(formData),
    ...(options || {}),
    onMutate: (vars) => {
      options?.onMutate?.(vars);
    },
    onSuccess: (...args) => {
      options?.onSuccess?.(...args);
    },
  });
};

export const useUpdateJobSimulationProgress = (
  options?: t.MutationOptions<
    t.TJobSimulationProgress,
    { jobSimulationId: string; [key: string]: any },
    unknown,
    unknown
  >,
): UseMutationResult<
  t.TJobSimulationProgress,
  unknown,
  { jobSimulationId: string; [key: string]: any },
  unknown
> => {
  return useMutation({
    mutationFn: (data: { jobSimulationId: string; [key: string]: any }) => {
      const { jobSimulationId, ...rest } = data;
      return dataService.updateJobSimulationProgress({ jobSimulationId, data: rest });
    },
    ...(options || {}),
    onMutate: (vars) => {
      options?.onMutate?.(vars);
    },
    onSuccess: (...args) => {
      options?.onSuccess?.(...args);
    },
  });
};

// Hook for job simulation app
export const useCreateJobSimulationApp = (
  options?: t.MutationOptions<any, FormData, unknown, unknown>,
): UseMutationResult<any, unknown, FormData, unknown> => {
  return useMutation([MutationKeys.createJobSimulationApp], {
    mutationFn: (payload) => dataService.createJobSimulationApp(payload),
    ...(options || {}),
    onMutate: (vars) => {
      options?.onMutate?.(vars);
    },
    onSuccess: (...args) => {
      options?.onSuccess?.(...args);
    },
  });
};

export const useUpdateJobSimulationApp = (
  options?: t.MutationOptions<any, { id: string } & FormData, unknown, unknown>,
): UseMutationResult<any, unknown, { id: string } & FormData, unknown> => {
  return useMutation([MutationKeys.updateJobSimulationApp], {
    mutationFn: (payload: { id: string } & FormData) => dataService.updateJobSimulationApp(payload.id, payload),
    ...(options || {}),
    onMutate: (vars) => {
      options?.onMutate?.(vars);
    },
    onSuccess: (...args) => {
      options?.onSuccess?.(...args);
    },
  });
};

export const useDeleleJobSimulationApp = (
  options?: t.MutationOptions<any, any, unknown, unknown>,
): UseMutationResult<any, unknown, any, unknown> => {
  return useMutation([MutationKeys.deleteJobSimulationApp], {
    mutationFn: (payload) => dataService.deleteJobSimulationApp(payload.id, payload),
    ...(options || {}),
    onMutate: (vars) => {
      options?.onMutate?.(vars);
    },
    onSuccess: (...args) => {
      options?.onSuccess?.(...args);
    },
  });
};
