import { Button } from "../../ui";

const TopSkillsTable = () => {
	const skills = [
		{
			rank: 1,
			skillName: "Data Interpretation",
			percentage: 89,
			medal: "🥇"
		},
		{
			rank: 2,
			skillName: "Critical Thinking",
			percentage: 85,
			medal: "🥈"
		},
		{
			rank: 3,
			skillName: "Marketing Strategy",
			percentage: 82,
			medal: "🥉"
		},
		{
			rank: 4,
			skillName: "Problem Solving",
			percentage: 79,
			medal: "4"
		},
		{
			rank: 5,
			skillName: "Communication",
			percentage: 76,
			medal: "5"
		},
	];

	return (
		<div className="bg-white p-6 rounded-xl drop-shadow-lg text-[#505050]">
			<div className="flex justify-between items-center mb-4">
				<h3 className="text-xl font-semibold">Top Skills In All Jobs</h3>
			</div>
			<hr />
			<div className="overflow-x-auto my-4">
				<table className="w-full">
					<thead className="border-b border-gray-200">
						<tr className="text-left">
							<th className="pb-3 text-sm font-light opacity-60 text-gray-500">Top</th>
							<th className="pb-3 text-sm font-light opacity-60 text-gray-500">Skill name</th>
							<th className="pb-3 text-sm font-light opacity-60 text-gray-500">% Users</th>
						</tr>
					</thead>
					<tbody className="divide-y divide-gray-100">
						{skills.map((skill) => (
							<tr key={skill.rank} className="hover:bg-gray-50">
								<td className="py-4">
									<span className="font-medium text-gray-500 text-sm">{skill.medal}</span>
								</td>
								<td className="py-4">
									<div className="font-medium text-gray-500 text-gray-600 text-sm">{skill.skillName}</div>
								</td>
								<td className="py-4">
									<div className="font-medium text-gray-500 text-gray-600 text-sm">{skill.percentage}%</div>
								</td>
							</tr>
						))}
					</tbody>
				</table>
				<hr />
			</div>
			<div className="flex justify-center mt-6">
				<Button variant="link" className="text-gray-600 font-light underline italic">
					View detail
				</Button>
			</div>
		</div>
	);
};

export default TopSkillsTable;