import React, { useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '~/components/ui';

interface MediaLibraryModalProps {
	isOpen: boolean;
	onClose: () => void;
	onSelect: (files: File[]) => void;
}

const sampleMediaFiles = [
	{ url: 'https://img.ltwebstatic.com/images3_spmp/2023/08/24/46/1692847502fbbeda99503708fa396d04424698ca68_thumbnail_720x.jpg' },
	{ url: 'https://img.ltwebstatic.com/images3_spmp/2023/10/22/b5/1697907046ad9bd9a78933b8e4b4586ac98c5809b5_thumbnail_720x.jpg' },
	{ url: 'https://m.media-amazon.com/images/I/6158In8ciFL._AC_UF1000,1000_QL80_.jpg' },
	{ url: 'https://m.media-amazon.com/images/I/616aXCKIf+L._AC_UF350,350_QL80_.jpg' },
	{ url: 'https://image.made-in-china.com/2f0j00onNVKUzaAIgd/Cheap-Stainless-Steel-Smart-Water-Bottle-with-LED-Temperature-Display-Thermo-Tumbler-Cups-in-Bulk.jpg' },
]
const MediaLibraryModal: React.FC<MediaLibraryModalProps> = ({
	isOpen,
	onClose,
	onSelect
}) => {
	const handleImageSelect = async (imageUrl: string, imageName: string) => {
		try {
			const response = await fetch(imageUrl);
			const blob = await response.blob();
			const file = new File([blob], imageName, { type: 'image/jpeg' });
			onSelect([file]);
			onClose();
		} catch (error) {
			console.error('Error loading image:', error);
		}
	};

	const [imageSizes, setImageSizes] = React.useState<Record<string, string>>({});

	useEffect(() => {
		const loadImageSizes = async () => {
			const sizes: Record<string, string> = {};
			await Promise.all(
				sampleMediaFiles.map(({ url }) => {
					return new Promise<void>((resolve) => {
						const img = new window.Image();
						img.onload = () => {
							sizes[url] = `${img.width}x${img.height}`;
							resolve();
						};
						img.onerror = () => {
							sizes[url] = 'Unknown';
							resolve();
						};
						img.src = url;
					});
				})
			);
			setImageSizes(sizes);
		};

		loadImageSizes();
	}, []);

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-5xl p-4">
				<p className='text-lg font-medium mb-4'>Choose photo</p>
				<div className="grid grid-cols-3 gap-4 max-h-[500px] overflow-y-auto p-3">
					{sampleMediaFiles.map((file, idx) => (
						<div
							key={file.url}
							onClick={() => handleImageSelect(file.url!, `image-${idx}.jpg`)}
							className="relative cursor-pointer hover:opacity-80 transition-opacity h-full"
						>
							<img
								src={file.url}
								alt={`image-${idx}`}
								className="w-full h-full object-contain rounded-lg"
							/>
							<div className="absolute bottom-2 left-2 text-xs text-white bg-black bg-opacity-50 px-2 py-1 rounded">
								{imageSizes[file.url] || 'Loading...'}
							</div>
						</div>
					))}
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default MediaLibraryModal;