import React, { useState } from 'react';
import { FlagTriangleLeftIcon, PhoneCall } from 'lucide-react';
import { Button, Dialog, DialogContent, DialogHeader, DialogTitle, RadioGroup, RadioGroupItem } from '~/components/ui';

interface GoalSelectionModalProps {
	isOpen: boolean;
	onClose: () => void;
	onSave: (goal: string) => void;
	currentGoal?: string;
}

const GoalSelectionModal: React.FC<GoalSelectionModalProps> = ({
	isOpen,
	onClose,
	onSave,
	currentGoal = 'traffic'
}) => {
	const [selectedGoal, setSelectedGoal] = useState(currentGoal);

	const handleSave = () => {
		onSave(selectedGoal);
		onClose();
	};

	const goals = [
		{
			id: 'page-likes',
			title: 'Get more Page likes',
			description: 'Create a promotion to help more people find and like your Page',
			category: 'Traffic',
			icon: FlagTriangleLeftIcon,
			color: 'bg-red-100',
			iconBg: 'bg-red-500'
		},
		{
			id: 'calls',
			title: 'Get more calls',
			description: 'Show your ad to people who are more likely to call your business',
			category: 'Sales',
			icon: PhoneCall,
			color: 'bg-purple-100',
			iconBg: 'bg-purple-500'
		}
	];

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-2xl p-0">
				<DialogHeader className="p-4">
					<div className="flex items-center justify-between">
						<DialogTitle className="text-xl font-medium">Goal</DialogTitle>
					</div>
				</DialogHeader>

				<div className="p-4 pt-0">
					<p className="mb-4">What results would you like from this ad?</p>

					<RadioGroup value={selectedGoal} onValueChange={setSelectedGoal}>
						{goals.map((goal) => {
							const IconComponent = goal.icon;
							return (
								<div key={goal.id} className="flex items-center space-x-4 p-4 hover:bg-gray-50 transition-colors cursor-pointer">
									<RadioGroupItem value={goal.id} className="mt-1" />
									<div className={`w-10 h-10 ${goal.iconBg} rounded-full flex items-center justify-center flex-shrink-0`}>
										<IconComponent className="w-6 h-6 text-white" />
									</div>
									<div className="flex-1">
										<div className="flex items-center space-x-2">
											<p className="font-medium">{goal.title}</p>
											<div className="w-4 h-4 bg-gray-400 rounded-full flex items-center justify-center">
												<span className="text-white text-xs">i</span>
											</div>
										</div>
										<p className="text-xs text-[#929294] mb-1">{goal.description}</p>
										<p className="text-xs text-[#929294]">Good for: <span className="font-medium text-black bg-gray-50 px-2 py-1 rounded-full">{goal.category}</span></p>
									</div>
								</div>
							);
						})}
					</RadioGroup>

					<div className="flex justify-end space-x-3 mt-8 pt-6 border-t">
						<Button variant="outline" onClick={onClose}>
							Cancel
						</Button>
						<Button className='bg-blue-500 hover:bg-blue-600 text-white' onClick={handleSave}>
							Save
						</Button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default GoalSelectionModal;