import JobSimulationPage from '~/components/JobSimulation/JobSimulationPage';
import JobSimulationStartupPage from '~/components/JobSimulation/JobSimulationStartupPage';
import ListJobSimulationPage from '~/components/JobSimulation/ListJobSimulationPage';
import RouteErrorBoundary from './RouteErrorBoundary';
import jobSimulationSettingRoutes from '~/components/JobSimulation/JobSimulationSettingPage/jobSimulationSettingRouter';
import DashboardPage from '~/components/Dashboard';
import JobSimulationDetail from '~/components/Dashboard/JobSimulationDetail';
import Dashboard from '~/components/Dashboard/Dashboard';
import { Navigate } from 'react-router-dom';
import RedirectToFirstTab from './RedirectToFirstTab';
import JobDetail from '~/components/Dashboard/JobSimulationDetail/JobSimulationCandidates';

const jobSimulationRoutes = {
  path: '/job-simulation',
  element: <JobSimulationStartupPage />,
  errorElement: <RouteErrorBoundary />,
  children: [
    {
      path: 'list',
      element: <ListJobSimulationPage />,
    },
    {
      path: ':jobSimulationId',
      element: <JobSimulationPage />,
    },
    {
      path: 'dashboard',
      element: <DashboardPage />,
      children: [
        { index: true, element: <Navigate to="overview" replace /> },
        { path: 'overview', element: <Dashboard /> },
        { path: 'candidates', element: <JobDetail /> },
      ],
    },
    {
      path: 'detail',
      element: <DashboardPage />,
      children: [
        {
          index: true,
          element: <RedirectToFirstTab />,
        },
        {
          path: ':tab',
          element: <JobSimulationDetail />,
        },
      ],
    },
    ...jobSimulationSettingRoutes,
  ],
};

export default jobSimulationRoutes;
