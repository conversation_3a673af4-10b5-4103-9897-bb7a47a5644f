const mongoose = require('mongoose');
const { jobSimulationFeedbackSchema } = require('@librechat/data-schemas');

const JobSimulationFeedback = mongoose.model('jobSimulationFeedback', jobSimulationFeedbackSchema);

/**
 * Create a new job simulation feedback
 * @param {Object} params - Feedback data
 * @returns {Promise<Object>} - Created feedback object
 */
const create = async function (params) {
  try {
    const feedback = await JobSimulationFeedback.create(params);
    return feedback;
  } catch (error) {
    throw new Error(error.message || `Failed to create job simulation feedback: ${error.message}`);
  }
};

/**
 * Update a job simulation feedback by ID
 * @param {string} id - Feedback ID to update
 * @param {Object} params - Parameters to update
 * @returns {Promise<Object>} - Updated feedback object
 */
const update = async function (id, params) {
  try {
    const feedback = await JobSimulationFeedback.findByIdAndUpdate(
      id,
      { $set: params },
      { new: true, runValidators: true },
    ).lean();

    if (!feedback) {
      throw new Error('Feedback not found');
    }

    return feedback;
  } catch (error) {
    throw new Error(error.message || `Failed to update job simulation feedback: ${error.message}`);
  }
};

/**
 * Delete a job simulation feedback by ID
 * @param {string} id - Feedback ID to delete
 * @returns {Promise<Object>} - Deleted feedback object
 */
const deleteById = async function (id) {
  try {
    const feedback = await JobSimulationFeedback.findByIdAndDelete(id).lean();

    if (!feedback) {
      throw new Error('Feedback not found');
    }

    return feedback;
  } catch (error) {
    throw new Error(`Failed to delete job simulation feedback: ${error.message}`);
  }
};

/**
 * Get a job simulation feedback by userId and jobSimulationId
 * @param {Object} params - Parameters for finding feedback
 * @param {string} params.userId - User ID
 * @param {string} params.jobSimulationId - Job simulation ID
 * @returns {Promise<Object>} - Feedback object
 */
const getByUserAndJobSimulation = async function (params) {
  try {
    const { userId, jobSimulationId } = params;

    const feedback = await JobSimulationFeedback.findOne({
      userId,
      jobSimulationId,
    }).lean();

    if (!feedback) {
      return null;
    }

    return feedback;
  } catch (error) {
    throw new Error(`Failed to retrieve job simulation feedback: ${error.message}`);
  }
};

/**
 * Get a job simulation feedback by ID
 * @param {string} id - Feedback ID
 * @returns {Promise<Object>} - Feedback object
 */
const getById = async function (id) {
  try {
    const feedback = await JobSimulationFeedback.findById(id).lean();

    return feedback;
  } catch (error) {
    throw new Error(`Failed to retrieve job simulation feedback: ${error.message}`);
  }
};

const calculateAvgScoresByJobSimulation = async function (params) {
  const { jobSimulationId, dateFrom, dateTo } = params;
  // TODO: What if user deletes the feedback? The history will be lost. Two reports will be different at the same filtered date (???)
  const filterCreatedAt = dateFrom || dateTo ? {
    createdAt: {
      ...(dateFrom ? { $gte: dateFrom } : {}),
      ...(dateTo ? { $lt: dateTo } : {}),
    },
  } : {};
  const filter = {
    jobSimulationId,
    ...(filterCreatedAt),
  };
  const result = await JobSimulationFeedback.aggregate([
    { $match: filter },
    { $group: { _id: null, avgScores: { $avg: "$scores" }, totalFeedback: { $sum: 1 }, totalScores: { $sum: "$scores" } } },
  ]);

  return result?.[0] || {};
}

module.exports = {
  model: JobSimulationFeedback,
  create,
  update,
  deleteById,
  getByUserAndJobSimulation,
  getById,
  calculateAvgScoresByJobSimulation,
};
