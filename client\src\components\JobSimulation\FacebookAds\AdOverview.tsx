import React, { useEffect, useState } from 'react';
import { ChevronDown, Info, Calendar, CheckCircle, Trash2 } from 'lucide-react';
import { Ava<PERSON>, Button } from '~/components/ui';

type Metric = {
	label: string;
	value: string;
	hasInfo: boolean;
};
const AdOverview = ({ onCreateAd }) => {
	const [metrics, setMetrics] = useState<Metric[]>([]);
	const [adsState, setAdsState] = useState(JSON.parse(localStorage.getItem('ads') || '[]'));
	const formatted = (time) => new Date(time).toLocaleString('en-US', {
		month: 'short',
		day: 'numeric',
		year: 'numeric',
	});
	const goalTitles = {
		'page-likes': 'Page likes',
		calls: 'Calls'
	};
	useEffect(() => {
		const totalViews = adsState.reduce((acc, ad) => acc + (ad.views || 0), 0);
		const totalReach = adsState.reduce((acc, ad) => acc + (ad.reach || 0), 0);
		const postEngagements = adsState.length > 0 ? Math.floor(Math.random() * 500) + 100 : 0;
		const linkClicks = adsState.length > 0 ? Math.floor(Math.random() * 200) + 50 : 0;

		setMetrics([
			{ label: 'Views', value: totalViews.toLocaleString(), hasInfo: true },
			{ label: 'Reach', value: totalReach.toLocaleString(), hasInfo: true },
			{ label: 'Post engagements', value: postEngagements.toLocaleString(), hasInfo: true },
			{ label: 'Link clicks', value: linkClicks.toLocaleString(), hasInfo: true }
		]);
	}, [adsState]);

	return (
		<div className='h-full'>
			<div>
				<div className='space-y-5'>
					<div className='space-y-5 bg-white rounded p-4 drop-shadow-lg'>
						<div className="flex items-center justify-between">
							<div>
								<p className="text-lg font-medium">Advertising summary</p>
								<p className='text-sm'>User spent ₫0.00 on {adsState?.length} ads in the last 60 days</p>
							</div>
							<div className="flex items-center space-x-2">
								<Button variant="outline" className="flex items-center space-x-1 font-normal">
									<Calendar className="w-4 h-4" />
									<span>60 days: Mar 31, 2025 - May 29, 2025</span>
									<ChevronDown className="w-4 h-4" />
								</Button>
							</div>
						</div>

						{/* Metrics Grid */}
						<div className="grid grid-cols-4 gap-4 mb-8">
							{metrics.map((metric, index) => (
								<div key={index} className="bg-white rounded-lg p-4 border">
									<div className="flex items-center space-x-2 mb-2">
										<span className="text-base font-medium">{metric.label}</span>
										{metric.hasInfo && <Info className="w-4 h-4" />}
									</div>
									<div className="text-2xl font-bold mb-2">{metric.value}</div>
									<Button variant="outline" size="sm" className="w-full">
										See more
									</Button>
								</div>
							))}
						</div>
					</div>
					{/* Block */}
					<div className="grid grid-cols-12 gap-4">
						{/* Empty State and Audiences Section */}
						<div className="col-span-8 space-y-5">
							{/* Empty State */}
							{adsState.length === 0 ? (
								<div className="bg-white rounded-lg p-8 text-center drop-shadow-lg">
									<div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
										<div className="w-12 h-12 bg-blue-200 rounded flex items-center justify-center">
											📊
										</div>
									</div>
									<h3 className="text-lg font-medium">View results</h3>
									<p className="text-base">
										After you start, you'll see data from your ads from the last 5 days.
									</p>
								</div>
							) :
								<div className="bg-white rounded-lg p-4 drop-shadow-lg">
									<p className='text-lg font-medium mb-4'>Recent ads</p>
									<div className='space-y-3'>
										{adsState?.map((ad, index) => (
											<div key={index} className='border rounded p-3 space-y-3'>
												<div className='flex items-center justify-between'>
													<div>
														<span className='bg-green-50 text-green-500 text-sm px-2 py-1 rounded-full font-medium'>Active</span>
														<span className='text-sm'> - {formatted(ad?.id)}</span>
													</div>
													<div className='flex items-center'>
														<Button variant="outline"
															size="sm"
															className="font-normal rounded px-3 mr-2">Edit</Button>
														<Button variant="ghost"
															size="sm"
															className='border rounded'
															onClick={() => {
																const updatedAds = adsState.filter((item) => item.id !== ad.id);
																localStorage.setItem('ads', JSON.stringify(updatedAds));
																setAdsState(updatedAds);
															}}><Trash2 className="w-4 h-4" /></Button>
													</div>
												</div>
												<hr />
												<div>
													<p className='text-xs text-gray-400 font-normal mb-2'>Ad</p>
													<div className='flex items-center justify-between'>
														<div className='flex items-center gap-3'>
															<img src={ad?.selectedImages[0]?.preview} alt="Ad image" className='w-10 h-10 object-cover rounded-full' />
															<div>
																<p className='text-base font-normal'>{goalTitles[ad?.goal]}</p>
																<p className='text-sm text-gray-400'>{ad?.postText}</p>
															</div>
														</div>
														<div>
															<p className='text-base font-normal'>{ad?.views}</p>
															<p className='text-sm text-gray-400'>Views</p>
														</div>
														<div>
															<p className='text-base font-normal'>{ad?.reach}</p>
															<p className='text-sm text-gray-400'>Reach</p>
														</div>
														<div className='text-base font-normal'>
															<p>--</p>
															<p className='text-sm text-gray-400 max-w-[150px]'>Estimated call confirmation clicks</p>
														</div>
														<div className='text-base font-normal'>
															<p>₫0</p>
															<p className='text-sm text-gray-400 max-w-[124px]'>Spent at ₫{ad?.budget.toLocaleString()} per day</p>
														</div>
													</div>
												</div>
											</div>
										))}
									</div>
								</div>
							}

							{/* Audiences Section */}
							<div>
								<div className="bg-white rounded-lg p-4 text-center drop-shadow-lg">
									<div className="flex items-center justify-between mb-4">
										<h2 className="text-lg font-medium">Audiences</h2>
										{/* <Button variant="outline" className="flex items-center space-x-1">
											<span className='font-normal'>Last 30 days</span>
											<ChevronDown className="w-4 h-4" />
										</Button> */}
									</div>
									<div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
										<div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
											👥
										</div>
									</div>
									<p className="text-base font-semibold mb-4">
										You’ll see audience insights here when 100{' '}
										<span className="text-blue-600 cursor-pointer">Accounts Center accounts</span>{' '}
										engage with your ads
									</p>
									<p className="max-w-2xl text-base mb-6 mx-auto">
										Post engagement audience insights can help you learn about the people taking action on your ads with a breakdown of age and gender, placements and locations.
									</p>
									<Button variant="outline" className='font-normal' onClick={onCreateAd}>Create Ad</Button>
								</div>
							</div>
						</div>
						{/* Hello Section */}
						<div className="col-span-4 space-y-5">
							<div className="bg-white rounded-lg p-4 drop-shadow-lg">
								<p className='text-base font-medium mb-4'>Recommendations</p>
								<div className='flex justify-center'>
									<div className='text-center max-w-[256px] max-auto'>
										<CheckCircle className='w-8 h-8 mx-auto text-green-500' />
										<p className='text-base font-semibold'>Completed</p>
										<p className='text-sm text-[#929294]'>Nice work! Check back soon for new personalized recommendations.</p>
									</div>
								</div>
							</div>
						</div>
					</div>
					{/* Block */}
				</div>
			</div>
			{/* )} */}
		</div>
	);
};

export default AdOverview;