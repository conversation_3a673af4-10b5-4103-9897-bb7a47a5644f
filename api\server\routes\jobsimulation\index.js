const express = require('express');
const router = express.Router();
const adminRoutes = require('./admin');
const progressRoutes = require('./progress');
const userRoutes = require('./user');
const userInteractionRoutes = require('./user-interaction');
const appRoutes = require('./app');
const employerRoutes = require('./employer');
const publicRoutes = require('./public');
const feedbackRoutes = require('./feedback');
const screenRoutes = require('./screen');

// Admin routes
router.use('/admin', adminRoutes);

// User interaction routes
router.use('/user-interaction', userInteractionRoutes);

// User progress
// TODO: refactor /user-interaction and /user-progress to /user
router.use('/user-progress', progressRoutes);
router.use('/user', userRoutes);
router.use('/employer', employerRoutes);

// Public routes
// TODO: add middleware check x-api-key
router.use('/public', publicRoutes);

// Job simulation app routes
router.use('/apps', appRoutes);

// Job simulation feedback routes
router.use('/feedbacks', feedbackRoutes);

// Job simulation app screen routes
router.use('/screens', screenRoutes);

module.exports = router;
