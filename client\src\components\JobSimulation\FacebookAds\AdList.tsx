import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ChevronDown, Search, Filter, MoreHorizontal, Calendar, Trash2 } from 'lucide-react';
import { Button } from '~/components/ui';
import DateRangeDropdown from './components/DateRangeDropdown';

const AdsList = ({ onGoToDraft, onGoToPostDetail }) => {
	const [showDateDropdown, setShowDateDropdown] = useState(false);
	const [selectedDateRange, setSelectedDateRange] = useState('Last 30 days');
	const [adsData, setAdsData] = useState<{ id: string;[key: string]: any }[]>([]);
	console.log("🚀 ~ AdsList ~ adsData:", adsData)

	const goalTitles = {
		'page-likes': 'Get more Page likes',
		calls: 'Get more calls'
	};
	const formatNumber = (num) =>
		num >= 1_000_000 ? (num / 1_000_000).toFixed(1) + 'M'
			: num >= 1_000 ? (num / 1_000).toFixed(1) + 'K'
				: num;

	const formatted = (time) => new Date(time).toLocaleString('en-US', {
		month: 'short',
		day: 'numeric',
		year: 'numeric',
		hour: 'numeric',
		minute: '2-digit',
		hour12: true,
	});

	const incrementMetrics = (base: number, maxStep: number) => {
		return base + Math.floor(Math.random() * maxStep + 1);
	};

	useEffect(() => {
		const storedAds = JSON.parse(localStorage.getItem('ads') || '[]');

		const updatedAds = storedAds.map((ad) => {
			const updated = { ...ad };

			// Nếu chưa có giá trị, khởi tạo
			if (!updated.views) updated.views = Math.floor(Math.random() * 100 + 100);
			if (!updated.reach) updated.reach = Math.floor(Math.random() * 200 + 200);
			if (!updated.results) updated.results = Math.floor(Math.random() * 50 + 20);

			// Lưu lại localStorage nếu cần
			return updated;
		});

		setAdsData(updatedAds);
		localStorage.setItem('ads', JSON.stringify(updatedAds));
	}, []);

	// Periodically update adsData values every 5 seconds
	useEffect(() => {
		const interval = setInterval(() => {
			setAdsData((prev) => {
				const newAds = prev.map((ad) => {
					const updated = {
						...ad,
						views: ad.views + Math.floor(Math.random() * 50 + 1),
						reach: ad.reach + Math.floor(Math.random() * 50 + 1),
						results: ad.results + Math.floor(Math.random() * 50 + 1),
					};
					return updated;
				});
				localStorage.setItem('ads', JSON.stringify(newAds));
				return newAds;
			});
		}, 5000);
		return () => clearInterval(interval);
	}, []);

	return (
		<div className='h-full'>
			{/* Main Content */}
			<div className='bg-white rounded-lg p-4 drop-shadow-lg'>
				{/* Header */}
				<div className="mb-6">
					<div className="flex items-center justify-between mb-4">
						<div className="flex items-center space-x-4">
							<div className="relative">
								<Search className="absolute left-3 top-1/2 transform -translate-y-1/2" />
								<input
									type="text"
									placeholder="Search"
									className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-80 focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
								/>
							</div>
							<Button variant="outline" className="flex items-center space-x-2">
								<Filter className="w-4 h-4" />
								<span>Filter</span>
							</Button>
							<Button variant="outline">Clear</Button>
						</div>

						<div className="relative">
							<Button
								variant="outline"
								onClick={() => setShowDateDropdown(!showDateDropdown)}
								className="flex items-center space-x-2 font-normal"
							>
								<Calendar className="w-4 h-4" />
								<span>Year: May 30, 2024 - May 29, 2025</span>
								<ChevronDown className="w-4 h-4" />
							</Button>

							{showDateDropdown && (
								<DateRangeDropdown
									selectedRange={selectedDateRange}
									onSelect={(range) => {
										setSelectedDateRange(range);
										setShowDateDropdown(false);
									}}
									onClose={() => setShowDateDropdown(false)}
								/>
							)}
						</div>
					</div>
				</div>

				{/* Ads Table */}
				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200 text-sm text-left">
						<thead className="bg-gray-100 text-gray-700">
							<tr>
								<th className="px-6 py-3 font-medium">Ads</th>
								<th className="px-6 py-3 font-medium">Views</th>
								<th className="px-6 py-3 font-medium">Reach</th>
								<th className="px-6 py-3 font-medium">Results</th>
								<th className="px-6 py-3 font-medium">Amount Spent</th>
								<th className="px-6 py-3"></th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-100">
							{adsData.length === 0 ? (
								<tr>
									<td colSpan={6} className="text-center py-10 text-gray-400">
										No ads found. Create your first ad to get started!
									</td>
								</tr>
							) : adsData && adsData.map((ad) => (
								<tr key={ad.id} className="hover:bg-gray-50">
									<td className="px-6 py-4">
										<div className='flex items-center cursor-pointer gap-3' onClick={() => onGoToPostDetail(ad)}>
											<div className="w-12 h-12 rounded-lg flex items-center justify-center text-white font-bold mr-3">
												<img src={ad?.logo} alt="Image" className='w-full h-full object-cover rounded-lg' />
											</div>
											<div className='text-[#929294] text-xs'>
												<p className='text-xs text-[#929294] mb-1'>Ad</p>
												<p>{formatted(ad?.id)}</p>
												<p className='text-base text-black'>{goalTitles[ad?.goal]}</p>
												<p>{ad?.postText}</p>
											</div>
											<p className='text-sm font-medium bg-[#e1edf7] rounded-xl text-center px-2 py-0.5'>Active</p>
										</div>
									</td>
									<td className="px-6 py-4 text-gray-700">
										<div>
											<p className='text-lg'>{formatNumber(incrementMetrics(ad.views, 10))}</p>
											<p className='text-xs text-[#929294]'>Views</p>
										</div>
									</td>
									<td className="px-6 py-4 text-gray-700">
										<div>
											<p className='text-lg'>{formatNumber(incrementMetrics(ad.reach, 20))}</p>
											<p className='text-xs text-[#929294]'>Reachs</p>
										</div>
									</td>
									<td className="px-6 py-4 text-gray-700">
										<div>
											<p className='text-lg'>{formatNumber(incrementMetrics(ad.results, 5))}</p>
											<p className='text-xs text-[#929294]'>Follows or likes</p>
										</div>
									</td>
									<td className="px-6 py-4 text-gray-700">
										<div>
											<p className='text-lg'>₫0</p>
											<p className='text-xs text-[#929294]'>Spent at ₫{ad?.budget?.toLocaleString()} per day</p>
										</div>
									</td>
									<td className="px-6 py-4 text-right">
										<div className='flex items-center justify-end'>
											<Button
												variant="outline"
												size="sm"
												className="font-normal rounded px-3 mr-2"
												onClick={() => onGoToDraft(ad.id)}
											>
												Edit
											</Button>
											{/* <Button
												variant="outline"
												size="sm"
												className="font-normal rounded px-3 mr-2"
											>
												View results
											</Button> */}
											<Button
												variant="ghost"
												size="sm"
												className='border rounded'
												onClick={() => {
													const updatedAds = adsData.filter((item) => item.id !== ad.id);
													setAdsData(updatedAds);
													localStorage.setItem('ads', JSON.stringify(updatedAds));
												}}
											>
												<Trash2 className="w-4 h-4" />
											</Button>
										</div>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>
		</div>
	);
};

export default AdsList;