import { useRef } from 'react';
import { Button, Dialog, Input, OGDialogTemplate, SelectDropDown } from '~/components/ui';
import { useForm, Controller } from 'react-hook-form';

interface SettingModalProps {
  open: boolean;
  onClose: () => void;
  onOk: (data: any) => void;
}

const contentTypes = [
  { value: 'elements', label: 'Element' },
  { value: 'charts', label: 'Chart' },
  { value: 'placeholders', label: 'Placeholder' },
];

const actionTypes = [
  { value: 'nextScreen', label: 'Next Screen' },
  { value: 'inputText', label: 'Input Text' },
  { value: 'dropdown', label: 'Dropdown' },
  { value: 'modal', label: 'Modal' },
  { value: 'triggerMessage', label: 'Trigger Message' },
  { value: 'uploadPhoto', label: 'Upload Photo' },
];

const inputTypes = [
  { value: 'textarea', label: 'Textarea' },
  { value: 'text', label: 'Text' },
];

const screens = [
  { value: '001', label: '001' },
  { value: '002', label: '002' },
  { value: '003', label: '003' },
  { value: '004', label: '004' },
];

const contexts = [
  { value: '001', label: '001' },
  { value: '002', label: '002' },
  { value: '003', label: '003' },
  { value: '004', label: '004' },
];

const placeholderTypes = [
  { value: 'text', label: 'Text' },
  { value: 'image', label: 'Image' },
];

const fontWeights = [
  { value: 'normal', label: 'Normal' },
  { value: 'bold', label: 'Bold' },
  { value: 'bolder', label: 'Bolder' },
  { value: 'lighter', label: 'Lighter' },
];

const alignItems = [
  { value: 'normal', label: 'Normal' },
  { value: 'stretch', label: 'Stretch' },
  { value: 'flex-start', label: 'Flex-Start' },
  { value: 'flex-end', label: 'Flex-End' },
  { value: 'start', label: 'Start' },
  { value: 'end', label: 'End' },
  { value: 'baseline', label: 'Baseline' },
  { value: 'initial', label: 'Initial' },
  { value: 'inherit', label: 'Inherit' },
];

const justifyContents = [
  { value: 'flex-start', label: 'Flex-Start' },
  { value: 'flex-end', label: 'Flex-End' },
  { value: 'center', label: 'Center' },
  { value: 'space-between', label: 'Space-Between' },
  { value: 'space-around', label: 'Space-Around' },
  { value: 'space-evenly', label: 'Space-Evenly' },
  { value: 'initial', label: 'Initial' },
  { value: 'inherit', label: 'Inherit' },
];

const whiteSpaces = [
  { value: 'normal', label: 'Normal' },
  { value: 'nowrap', label: 'Nowrap' },
  { value: 'pre', label: 'Pre' },
  { value: 'pre-line', label: 'Pre-line' },
  { value: 'pre-wrap', label: 'Pre-wrap' },
  { value: 'initial', label: 'Initial' },
  { value: 'inherit', label: 'Inherit' },
];

const textOverflows = [
  { value: 'clip', label: 'Clip' },
  { value: 'ellipsis', label: 'Ellipsis' },
  { value: 'string', label: 'String' },
  { value: 'initial', label: 'Initial' },
  { value: 'inherit', label: 'Inherit' },
];

const overflows = [
  { value: 'visible', label: 'Visible' },
  { value: 'hidden', label: 'Hidden' },
  { value: 'clip', label: 'Clip' },
  { value: 'scroll', label: 'Scroll' },
  { value: 'auto', label: 'Auto' },
  { value: 'initial', label: 'Initial' },
  { value: 'inherit', label: 'Inherit' },
];

const typeChartLines = [
  { value: 'monotone', label: 'Monotone' },
  { value: 'basis', label: 'Basis' },
  { value: 'bump', label: 'Bump' },
  { value: 'natural', label: 'Natural' },
  { value: 'step', label: 'Step' },
];

export default function SettingModal({ open, onClose, onOk }: SettingModalProps) {
  const screenBgColorRef = useRef(null);
  const actionBgColorRef = useRef(null);
  const actionColorRef = useRef(null);
  const actionBorderColorRef = useRef(null);
  const chartStrokeColorRef = useRef(null);

  const {
    register,
    handleSubmit,
    setValue,
    getValues,
    control,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: {
      title: '',
      screenBgColor: '#64767e',
      contentType: contentTypes[0],
      actionBgColor: '',
      actionColor: '',
      actionBorderColor: '',
      actions: [
        {
          actionType: actionTypes[0],
          message: '',
          inputType: null,
          screen: null,
          contextId: '',
          contextLabel: '',
          dropdown: [
            {
              name: '',
              value: '',
              contextId: '',
              contextLabel: '',
            },
          ],
        },
      ],
      contextId: '',
      placeholderType: placeholderTypes[0],
      fontSize: '',
      fontWeight: null,
      alignItems: null,
      justifyContent: null,
      whiteSpace: null,
      textOverflow: null,
      overflow: null,
      dataByTime: [],
      labelX: '',
      labelY: '',
      timePoints: '',
      xLabels: '',
      lines: [
        {
          name: '',
          contextId: '',
          type: typeChartLines[0],
          stroke: '',
          staticValues: '',
        },
      ],
    },
  });

  const watchContentType = watch('contentType');
  const watchActions = watch('actions');
  const watchDataByTime = watch('dataByTime');
  const watchLines = watch('lines');

  const onSubmit = (data: any) => {
    if (typeof onOk === 'function') {
      onOk(data);
    }
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <OGDialogTemplate
        className="max-h-[80vh] w-full max-w-xl overflow-y-auto"
        title="Setting"
        main={
          <div className="flex flex-col gap-4 text-white">
            <div>
              <Input
                type="text"
                placeholder="Enter title"
                className="w-full border border-[#424242]"
                {...register('title', {
                  required: 'Title is required',
                  maxLength: {
                    value: 100,
                    message: 'Title cannot exceed 100 characters',
                  },
                })}
              />
              {errors?.title && (
                <span className="text-xs text-red-500">{errors?.title?.message}</span>
              )}
            </div>

            <div>
              <Controller
                name="contentType"
                control={control}
                rules={{ required: 'Content type is required' }}
                defaultValue={contentTypes[0]}
                render={({ field }) => (
                  <SelectDropDown
                    value={field.value}
                    setValue={(data) => field.onChange(data)}
                    availableValues={contentTypes}
                    showLabel={false}
                    showAbove={false}
                    emptyTitle={true}
                  />
                )}
              />
              {errors?.contentType && (
                <span className="text-xs text-red-500">{errors?.contentType?.message}</span>
              )}
            </div>

            <div>
              <div>Screen:</div>
              <div className="relative inline-block">
                <div className="flex items-center gap-2">
                  <div>Background color</div>
                  <div
                    className="h-6 w-12 cursor-pointer rounded border border-gray-300"
                    style={{ backgroundColor: watch(`screenBgColor`) }}
                    onClick={() => screenBgColorRef?.current?.click()}
                  />
                </div>

                <Controller
                  name={`screenBgColor`}
                  control={control}
                  rules={{ required: false }}
                  render={({ field }) => (
                    <input
                      type="color"
                      value={field.value}
                      onChange={(e) => {
                        field.onChange(e.target.value);
                      }}
                      className="absolute left-0 top-0 h-full w-full cursor-pointer opacity-0"
                      ref={screenBgColorRef}
                    />
                  )}
                />
              </div>
            </div>

            {['elements', 'placeholders'].includes(watchContentType?.value) && (
              <div>
                <div>{watchContentType?.label}:</div>
                <div className="flex flex-wrap gap-3">
                  <div className="relative inline-block">
                    <div className="flex items-center gap-2">
                      <div>Background color</div>
                      <div
                        className="h-6 w-12 cursor-pointer rounded border border-gray-300"
                        style={{ backgroundColor: watch(`actionBgColor`) }}
                        onClick={() => actionBgColorRef?.current?.click()}
                      />
                    </div>

                    <Controller
                      name={`actionBgColor`}
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <input
                          type="color"
                          value={field.value}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                          }}
                          className="absolute left-0 top-0 h-full w-full cursor-pointer opacity-0"
                          ref={actionBgColorRef}
                        />
                      )}
                    />
                  </div>
                  <div className="relative inline-block">
                    <div className="flex items-center gap-2">
                      <div>Text color</div>
                      <div
                        className="h-6 w-12 cursor-pointer rounded border border-gray-300"
                        style={{ backgroundColor: watch(`actionColor`) }}
                        onClick={() => actionColorRef?.current?.click()}
                      />
                    </div>

                    <Controller
                      name={`actionColor`}
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <input
                          type="color"
                          value={field.value}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                          }}
                          className="absolute left-0 top-0 h-full w-full cursor-pointer opacity-0"
                          ref={actionColorRef}
                        />
                      )}
                    />
                  </div>
                  <div className="relative inline-block">
                    <div className="flex items-center gap-2">
                      <div>Border color</div>
                      <div
                        className="h-6 w-12 cursor-pointer rounded border border-gray-300"
                        style={{
                          backgroundColor: watch(`actionBorderColor`),
                        }}
                        onClick={() => actionBorderColorRef?.current?.click()}
                      />
                    </div>

                    <Controller
                      name={`actionBorderColor`}
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <input
                          type="color"
                          value={field.value}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                          }}
                          className="absolute left-0 top-0 h-full w-full cursor-pointer opacity-0"
                          ref={actionBorderColorRef}
                        />
                      )}
                    />
                  </div>
                </div>
              </div>
            )}

            {watchContentType?.value === 'elements' ? (
              <div>
                <div className="flex items-center justify-between">
                  <div>Action:</div>
                  <button
                    type="button"
                    className="flex items-center justify-center rounded-md px-2 py-1 hover:bg-red-600/20"
                    onClick={() => {
                      const currentActions = getValues('actions');
                      setValue('actions', [
                        ...currentActions,
                        {
                          actionType: actionTypes[0],
                          message: '',
                          inputType: null,
                          screen: null,
                          contextId: '',
                          contextLabel: '',
                          dropdown: [
                            {
                              name: '',
                              value: '',
                              contextId: '',
                              contextLabel: '',
                            },
                          ],
                        },
                      ]);
                    }}
                  >
                    <span>Add Action</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <line x1="12" y1="5" x2="12" y2="19"></line>
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                  </button>
                </div>

                <div className="mt-1 flex flex-col gap-4">
                  {watchActions?.map((_, index) => (
                    <div
                      className="relative flex flex-col gap-4 rounded-md border border-[#424242] p-4"
                      key={index}
                    >
                      <div className="absolute right-[-12px] top-[-12px]">
                        <button
                          type="button"
                          className="flex h-8 w-8 items-center justify-center rounded-full hover:bg-red-600/20"
                          disabled={watchActions.length <= 1}
                          onClick={() => {
                            const currentDropdown = getValues(`actions`);
                            setValue(
                              `actions`,
                              currentDropdown.filter((_, i) => i !== index),
                            );
                          }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                          </svg>
                        </button>
                      </div>

                      <div>
                        <Controller
                          name={`actions[${index}].actionType`}
                          control={control}
                          rules={{ required: 'Action type is required' }}
                          defaultValue={actionTypes[0]}
                          render={({ field }) => (
                            <SelectDropDown
                              value={field.value}
                              setValue={(data) => field.onChange(data)}
                              availableValues={actionTypes}
                              showLabel={false}
                              showAbove={false}
                              emptyTitle={true}
                            />
                          )}
                        />
                        {errors?.actions?.[index]?.actionType && (
                          <span className="text-xs text-red-500">
                            {errors?.actions?.[index]?.actionType?.message}
                          </span>
                        )}
                      </div>

                      <Input
                        type="text"
                        placeholder="Enter message"
                        className="w-full border-[#424242]"
                        {...register(`actions[${index}].message`, {
                          required: false,
                        })}
                      />

                      <Controller
                        name={`actions[${index}].inputType`}
                        control={control}
                        rules={{ required: false }}
                        defaultValue={inputTypes[0]}
                        render={({ field }) => (
                          <SelectDropDown
                            value={field.value}
                            setValue={(data) => field.onChange(data)}
                            availableValues={inputTypes}
                            showLabel={false}
                            showAbove={false}
                            emptyTitle={true}
                            placeholder="Select input type"
                          />
                        )}
                      />

                      <Controller
                        name={`actions[${index}].screen`}
                        control={control}
                        rules={{ required: false }}
                        defaultValue={screens[0]}
                        render={({ field }) => (
                          <SelectDropDown
                            value={field.value}
                            setValue={(data) => field.onChange(data)}
                            availableValues={screens}
                            showLabel={false}
                            showAbove={false}
                            emptyTitle={true}
                            placeholder="Select screen"
                          />
                        )}
                      />

                      <Input
                        type="text"
                        placeholder="Enter context ID"
                        className="w-full border-[#424242]"
                        {...register(`actions[${index}].contextId`, {
                          required: false,
                        })}
                      />

                      <Input
                        type="text"
                        placeholder="Enter context label"
                        className="w-full border-[#424242]"
                        {...register(`actions[${index}].contextLabel`, {
                          required: false,
                        })}
                      />

                      {/* <Controller
                        name={`actions[${index}].context`}
                        control={control}
                        rules={{ required: false }}
                        defaultValue={contexts[0]}
                        render={({ field }) => (
                          <SelectDropDown
                            value={field.value}
                            setValue={(data) => field.onChange(data)}
                            availableValues={contexts}
                            showLabel={false}
                            showAbove={false}
                            emptyTitle={true}
                            placeholder="Select context"
                          />
                        )}
                      /> */}

                      {watch(`actions[${index}].actionType`)?.value === 'dropdown' && (
                        <div>
                          <div className="mb-1 flex items-center justify-between">
                            <div>Dropdown Option:</div>
                            <button
                              type="button"
                              className="flex items-center justify-center rounded-md px-2 py-1 hover:bg-red-600/20"
                              onClick={() => {
                                const currentDropdown =
                                  getValues(`actions[${index}].dropdown`) || [];

                                setValue(`actions[${index}].dropdown`, [
                                  ...currentDropdown,
                                  { name: '', value: '', contextId: '', contextLabel: '' },
                                ]);
                              }}
                            >
                              <span>Add Option</span>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              >
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                              </svg>
                            </button>
                          </div>

                          <div
                            className={`flex flex-col gap-4 ${watch(`actions[${index}].dropdown`)?.length > 0 ? 'rounded-md border border-[#424242] p-4' : ''} `}
                          >
                            {watch(`actions[${index}].dropdown`)?.map((_, idx) => (
                              <div key={`dropdown_${idx}`} className="flex justify-between gap-4">
                                <div className="flex flex-1 flex-col gap-2">
                                  <div className="flex gap-4">
                                    <div className="flex-1">
                                      <Input
                                        type="text"
                                        placeholder="Enter name"
                                        className="w-full border-[#424242]"
                                        {...register(`actions.${index}.dropdown.${idx}.name`, {
                                          required:
                                            'Name is required and cannot exceed 50 characters',
                                          maxLength: 50,
                                        })}
                                      />
                                      {errors?.actions?.[index].dropdown?.[idx]?.name && (
                                        <span className="text-xs text-red-500">
                                          {errors.actions[index].dropdown[idx].name.message}
                                        </span>
                                      )}
                                    </div>
                                    <div className="flex-1">
                                      <Input
                                        type="text"
                                        placeholder="Enter value"
                                        className="w-full border-[#424242]"
                                        {...register(`actions.${index}.dropdown.${idx}.value`, {
                                          required:
                                            'Value is required and cannot exceed 50 characters',
                                          maxLength: 50,
                                        })}
                                      />
                                      {errors?.actions?.[index].dropdown?.[idx]?.value && (
                                        <span className="text-xs text-red-500">
                                          {errors.actions[index].dropdown[idx].value.message}
                                        </span>
                                      )}
                                    </div>
                                  </div>

                                  <div className="flex gap-4">
                                    <div className="flex-1">
                                      <Input
                                        type="text"
                                        placeholder="Enter context ID"
                                        className="w-full border-[#424242]"
                                        {...register(`actions.${index}.dropdown.${idx}.contextId`, {
                                          required:
                                            'contextId is required and cannot exceed 50 characters',
                                          maxLength: 50,
                                        })}
                                      />
                                      {errors?.actions?.[index].dropdown?.[idx]?.contextId && (
                                        <span className="text-xs text-red-500">
                                          {errors.actions[index].dropdown[idx].contextId.message}
                                        </span>
                                      )}
                                    </div>
                                    <div className="flex-1">
                                      <Input
                                        type="text"
                                        placeholder="Enter context label"
                                        className="w-full border-[#424242]"
                                        {...register(
                                          `actions.${index}.dropdown.${idx}.contextLable`,
                                          {
                                            required:
                                              'contextLable is required and cannot exceed 50 characters',
                                            maxLength: 50,
                                          },
                                        )}
                                      />
                                      {errors?.actions?.[index].dropdown?.[idx]?.contextLable && (
                                        <span className="text-xs text-red-500">
                                          {errors.actions[index].dropdown[idx].contextLable.message}
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                </div>

                                {/* Nút xoá bên phải */}
                                <button
                                  type="button"
                                  className="flex h-8 w-8 items-center justify-center self-start rounded-full hover:bg-red-600/20"
                                  onClick={() => {
                                    const currentDropdown = getValues(`actions.${index}.dropdown`);
                                    setValue(
                                      `actions.${index}.dropdown`,
                                      currentDropdown.filter((_, i) => i !== idx),
                                    );
                                  }}
                                >
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="16"
                                    height="16"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  >
                                    <line x1="18" y1="6" x2="6" y2="18" />
                                    <line x1="6" y1="6" x2="18" y2="18" />
                                  </svg>
                                </button>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ) : watchContentType?.value === 'placeholders' ? (
              <div className="flex flex-col gap-4 text-white">
                <div className="grid grid-cols-3 gap-4">
                  <Input
                    type="text"
                    placeholder="Enter font size"
                    className="w-full border-[#424242]"
                    {...register(`fontSize`, {
                      required: false,
                    })}
                  />

                  <Controller
                    name={`fontWeight`}
                    control={control}
                    render={({ field }) => (
                      <SelectDropDown
                        value={field.value}
                        setValue={(data) => field.onChange(data)}
                        availableValues={fontWeights}
                        showLabel={false}
                        showAbove={false}
                        emptyTitle={true}
                        placeholder="Select font weight"
                        className="w-full"
                      />
                    )}
                  />

                  <Controller
                    name={`alignItems`}
                    control={control}
                    render={({ field }) => (
                      <SelectDropDown
                        value={field.value}
                        setValue={(data) => field.onChange(data)}
                        availableValues={alignItems}
                        showLabel={false}
                        showAbove={false}
                        emptyTitle={true}
                        placeholder="Select align items"
                        className="w-full"
                      />
                    )}
                  />

                  <Controller
                    name={`justifyContent`}
                    control={control}
                    render={({ field }) => (
                      <SelectDropDown
                        value={field.value}
                        setValue={(data) => field.onChange(data)}
                        availableValues={justifyContents}
                        showLabel={false}
                        showAbove={false}
                        emptyTitle={true}
                        placeholder="Select justify content"
                        className="w-full"
                      />
                    )}
                  />

                  <Controller
                    name={`whiteSpace`}
                    control={control}
                    render={({ field }) => (
                      <SelectDropDown
                        value={field.value}
                        setValue={(data) => field.onChange(data)}
                        availableValues={whiteSpaces}
                        showLabel={false}
                        showAbove={false}
                        emptyTitle={true}
                        placeholder="Select white space"
                        className="w-full"
                      />
                    )}
                  />

                  <Controller
                    name={`textOverflow`}
                    control={control}
                    render={({ field }) => (
                      <SelectDropDown
                        value={field.value}
                        setValue={(data) => field.onChange(data)}
                        availableValues={textOverflows}
                        showLabel={false}
                        showAbove={false}
                        emptyTitle={true}
                        placeholder="Select text overflow"
                        className="w-full"
                      />
                    )}
                  />

                  <Controller
                    name={`overflow`}
                    control={control}
                    render={({ field }) => (
                      <SelectDropDown
                        value={field.value}
                        setValue={(data) => field.onChange(data)}
                        availableValues={overflows}
                        showLabel={false}
                        showAbove={false}
                        emptyTitle={true}
                        placeholder="Select text overflow"
                        className="w-full"
                      />
                    )}
                  />
                </div>

                <div>
                  <Controller
                    name={`placeholderType`}
                    control={control}
                    rules={{ required: 'Placeholder type is required' }}
                    defaultValue={placeholderTypes[0]}
                    render={({ field }) => (
                      <SelectDropDown
                        value={field.value}
                        setValue={(data) => field.onChange(data)}
                        availableValues={placeholderTypes}
                        showLabel={false}
                        showAbove={false}
                        emptyTitle={true}
                      />
                    )}
                  />
                  {errors?.placeholderType && (
                    <span className="text-xs text-red-500">{errors?.placeholderType?.message}</span>
                  )}
                </div>

                <Input
                  type="text"
                  placeholder="Enter context ID"
                  className="w-full border-[#424242]"
                  {...register(`contextId`, {
                    required: false,
                  })}
                />

                <div>
                  <div className="mb-1 flex items-center justify-between">
                    <div>Data by time:</div>
                    <button
                      type="button"
                      className="flex items-center justify-center rounded-md px-2 py-1 hover:bg-red-600/20"
                      onClick={() => {
                        const currentData = getValues(`dataByTime`) || [];
                        setValue(`dataByTime`, [...currentData, { name: '', value: '' }]);
                      }}
                    >
                      <span>Add Option</span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                      </svg>
                    </button>
                  </div>

                  <div
                    className={`flex flex-col gap-4 ${watchDataByTime?.length > 0 ? 'rounded-md border border-[#424242] p-4' : ''} `}
                  >
                    {watchDataByTime?.map((_, idx) => (
                      <div key={`dataByTime_${idx}`} className="flex justify-between gap-4">
                        <div className="flex flex-1 flex-col gap-2">
                          <div className="flex gap-4">
                            <div className="flex-1">
                              <Input
                                type="text"
                                placeholder="Enter name"
                                className="w-full border-[#424242]"
                                {...register(`dataByTime.${idx}.name`, {
                                  required: 'Name is required and cannot exceed 50 characters',
                                  maxLength: 50,
                                })}
                              />
                              {errors?.dataByTime?.[idx]?.name && (
                                <span className="text-xs text-red-500">
                                  {errors.dataByTime[idx].name.message}
                                </span>
                              )}
                            </div>
                            <div className="flex-1">
                              <Input
                                type="text"
                                placeholder="Enter value"
                                className="w-full border-[#424242]"
                                {...register(`dataByTime.${idx}.value`, {
                                  required: 'Value is required and cannot exceed 50 characters',
                                  maxLength: 50,
                                })}
                              />
                              {errors?.dataByTime?.[idx]?.value && (
                                <span className="text-xs text-red-500">
                                  {errors.dataByTime[idx].value.message}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>

                        <button
                          type="button"
                          className="flex h-8 w-8 items-center justify-center self-start rounded-full hover:bg-red-600/20"
                          onClick={() => {
                            const currentDropdown = getValues(`dataByTime`);
                            setValue(
                              `dataByTime`,
                              currentDropdown.filter((_, i) => i !== idx),
                            );
                          }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <line x1="18" y1="6" x2="6" y2="18" />
                            <line x1="6" y1="6" x2="18" y2="18" />
                          </svg>
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : watchContentType?.value === 'charts' ? (
              <div className="flex flex-col gap-4 text-white">
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    type="text"
                    placeholder="Enter label X"
                    className="w-full border-[#424242]"
                    {...register(`labelX`, {
                      required: false,
                    })}
                  />

                  <Input
                    type="text"
                    placeholder="Enter label Y"
                    className="w-full border-[#424242]"
                    {...register(`labelY`, {
                      required: false,
                    })}
                  />
                </div>

                <Input
                  type="text"
                  placeholder="Enter time points (comma separated)"
                  className="w-full border-[#424242]"
                  {...register(`timePoints`, {
                    required: false,
                  })}
                />

                <Input
                  type="text"
                  placeholder="Enter X labels (comma separated)"
                  className="w-full border-[#424242]"
                  {...register(`xLabels`, {
                    required: false,
                  })}
                />

                <div>
                  <div className="mb-1 flex items-center justify-between">
                    <div>Lines:</div>
                    <button
                      type="button"
                      className="flex items-center justify-center rounded-md px-2 py-1 hover:bg-red-600/20"
                      onClick={() => {
                        const currentData = getValues(`lines`) || [];
                        setValue(`lines`, [
                          ...currentData,
                          {
                            name: '',
                            contextId: '',
                            type: typeChartLines[0],
                            stroke: '',
                            staticValues: '',
                          },
                        ]);
                      }}
                    >
                      <span>Add Option</span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                      </svg>
                    </button>
                  </div>

                  <div
                    className={`flex flex-col gap-4 ${watchLines?.length > 0 ? 'rounded-md border border-[#424242] p-4' : ''} `}
                  >
                    {watchLines?.map((_, idx) => (
                      <div key={`lines_${idx}`} className="flex justify-between gap-4">
                        <div className="flex flex-1 flex-col gap-2">
                          <div className="flex gap-4">
                            <div className="flex-1">
                              <Input
                                type="text"
                                placeholder="Enter name"
                                className="w-full border-[#424242]"
                                {...register(`lines.${idx}.name`, {
                                  required: 'Name is required and cannot exceed 50 characters',
                                  maxLength: 50,
                                })}
                              />
                              {errors?.lines?.[idx]?.name && (
                                <span className="text-xs text-red-500">
                                  {errors.lines[idx].name.message}
                                </span>
                              )}
                            </div>

                            <div className="flex-1">
                              <Input
                                type="text"
                                placeholder="Enter context ID"
                                className="w-full border-[#424242]"
                                {...register(`lines.${idx}.contextId`, {
                                  required: false,
                                })}
                              />
                            </div>
                          </div>

                          <div className="flex items-center gap-4">
                            <div className="flex-1">
                              <Controller
                                name={`lines.${idx}.type`}
                                control={control}
                                rules={{ required: 'Chart type is required' }}
                                defaultValue={typeChartLines[0]}
                                render={({ field }) => (
                                  <SelectDropDown
                                    value={field.value}
                                    setValue={(data) => field.onChange(data)}
                                    availableValues={typeChartLines}
                                    showLabel={false}
                                    showAbove={false}
                                    emptyTitle={true}
                                  />
                                )}
                              />
                              {errors?.placeholderType && (
                                <span className="text-xs text-red-500">
                                  {errors?.placeholderType?.message}
                                </span>
                              )}
                            </div>

                            <div className="relative inline-block flex-1">
                              <div className="flex items-center gap-2">
                                <div>Stroke</div>
                                <div
                                  className="h-6 w-12 cursor-pointer rounded border border-gray-300"
                                  style={{ backgroundColor: watch(`lines.${idx}.stroke`) }}
                                  onClick={() => chartStrokeColorRef?.current?.click()}
                                />
                              </div>

                              <Controller
                                name={`lines.${idx}.stroke`}
                                control={control}
                                rules={{ required: false }}
                                render={({ field }) => (
                                  <input
                                    type="color"
                                    value={field.value}
                                    onChange={(e) => {
                                      field.onChange(e.target.value);
                                    }}
                                    className="absolute left-0 top-0 h-full w-full cursor-pointer opacity-0"
                                    ref={chartStrokeColorRef}
                                  />
                                )}
                              />
                            </div>
                          </div>

                          <div className="">
                            <Input
                              type="text"
                              placeholder="Enter static value (comma separated)"
                              className="w-full border-[#424242]"
                              {...register(`lines.${idx}.staticValues`, {
                                required: false,
                              })}
                            />
                            {errors?.lines?.[idx]?.staticValues && (
                              <span className="text-xs text-red-500">
                                {errors.lines[idx].staticValues.message}
                              </span>
                            )}
                          </div>
                        </div>

                        <button
                          type="button"
                          className="flex h-8 w-8 items-center justify-center self-start rounded-full hover:bg-red-600/20"
                          onClick={() => {
                            const currentLines = getValues(`lines`);
                            setValue(
                              `lines`,
                              currentLines.filter((_, i) => i !== idx),
                            );
                          }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <line x1="18" y1="6" x2="6" y2="18" />
                            <line x1="6" y1="6" x2="18" y2="18" />
                          </svg>
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              ''
            )}
          </div>
        }
        buttons={
          <div>
            <Button onClick={handleSubmit(onSubmit)}>OK</Button>
          </div>
        }
      />
    </Dialog>
  );
}
