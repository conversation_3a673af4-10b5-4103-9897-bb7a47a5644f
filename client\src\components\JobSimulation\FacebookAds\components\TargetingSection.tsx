import React, { useState } from 'react';
import { ChevronDown, Info, Edit, Copy, Trash2, CheckSquareIcon, XCircle } from 'lucide-react';
import { Button, } from '~/components/ui';

const TargetingSection = () => {
	const [advantagePlusEnabled, setAdvantagePlusEnabled] = useState(true);
	const [specialCategoryEnabled, setSpecialCategoryEnabled] = useState(false);

	return (
		<div className='bg-white drop-shadow-lg rounded-lg p-4'>
			<div className="flex items-center justify-between mb-4">
				<span className='text-lg font-medium'>Messaging</span>
				<Info className="w-6 h-6 text-gray-400" />
			</div>
			<div className="space-y-6">
				{/* Message Template */}
				<div>
					<h4 className="text-base font-medium">Message Template</h4>
					<p className="text-xs text-[#929294] mb-4">
						How do you want to welcome people who tap on your ad? Select a template
					</p>
					<div className="mt-4 flex items-center space-x-2 w-full">
						<Button variant="outline" className="flex items-center justify-between w-full font-normal">
							<span>Default message greeting template</span>
							<ChevronDown className="w-4 h-4" />
						</Button>
						<Button variant="outline" size="sm" className='font-normal'>
							<span>+ Create New</span>
						</Button>
					</div>
				</div>

				{/* Greeting */}
				<div className='bg-gray-50 rounded-lg p-3'>
					<div className="flex items-center justify-between">
						<h4 className="text-lg font-normal">Greeting</h4>
						<div className="flex items-center space-x-2">
							<Button variant="ghost" size="sm">
								<Edit className="w-4 h-4" />
							</Button>
							<Button variant="ghost" size="sm">
								<Copy className="w-4 h-4" />
							</Button>
							<Button variant="ghost" size="sm">
								<Trash2 className="w-4 h-4" />
							</Button>
						</div>
					</div>
					<p className="text-sm text-gray-600">
						Hello! We can help you. How can I help you?
					</p>
					{/* Questions */}
					<div className='mt-6'>
						<h4 className="text-lg font-normal mb-2">Questions</h4>
						<div className="space-y-2 mb-4">
							<div className="text-xs">1. Can I learn more about your business?.</div>
							<div className="text-xs">2. Can you tell me more about your ad?</div>
							<div className="text-xs">3. Is anyone available to chat?</div>
						</div>
						<p className="text-xs"><span className='text-[#929294]'>Automated responses:</span> <span>Off</span></p>
					</div>
				</div>
				<hr />
				{/* Applications */}
				{/* <div>
					<div className='mb-4'>
						<h4 className="text-lg font-medium">Apps</h4>
						<p className="text-xs text-[#929294]">
							Where do you want to invite people to message you?
						</p>
					</div>

					<div className="space-y-5">
						<div className="flex items-center justify-between">
							<div className="flex items-center space-x-3 text-base">
								<div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
									<span className="text-white text-sm">💬</span>
								</div>
								<span className="font-medium">Messenger</span>
							</div>
							<CheckSquareIcon />
						</div>

						<div className="flex items-center justify-between">
							<div className="flex items-center space-x-3 text-base">
								<div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
									<span className="text-white text-sm">📷</span>
								</div>
								<span className="font-medium">Instagram</span>
							</div>
							<Button size="sm" className="text-white bg-blue-500 hover:bg-blue-600 rounded">
								Connect
							</Button>
						</div>

						<div className="border-l-4 border-green-500 bg-white drop-shadow p-4 rounded-lg">
							<div className="flex items-center justify-between mb-2">
								<div className='flex items-center gap-3'>
									<Info className="w-5 h-5 text-green-600 mt-0.5" />
									<p className="text-sm font-medium">
										Connect your Instagram account for better performance
									</p>
								</div>
								<XCircle className="w-6 h-6 text-gray-400 cursor-pointer" />
							</div>
							<p className="text-sm mb-4">
								Optimize your ad campaigns by connecting your professional account to create ads that click to Instagram, Messenger or WhatsApp.
							</p>
							<Button
								variant="outline"
								className="w-full font-normal"
							>
								Connect account
							</Button>
						</div>

						<div className="flex items-center justify-between">
							<div className="flex items-center space-x-3 text-base">
								<div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
									<span className="text-white text-sm">📱</span>
								</div>
								<div>
									<span className="font-medium">WhatsApp</span>
									<p className="text-xs text-gray-500">Connect your account to use WhatsApp</p>
								</div>
							</div>
							<Button size="sm" className="text-white bg-blue-500 hover:bg-blue-600 rounded">
								Connect
							</Button>
						</div>
					</div>
				</div> */}
			</div>
		</div>
	);
};

export default TargetingSection;