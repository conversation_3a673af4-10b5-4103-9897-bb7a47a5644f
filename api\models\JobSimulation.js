const mongoose = require('mongoose');
const { jobSimulationSchema } = require('@librechat/data-schemas');
// const { logger } = require('~/config');

const JobSimulation = mongoose.model('jobSimulation', jobSimulationSchema);

// TODO: For now, use a hardcoded list of job simulation IDs.
const LIST_JS_IDS = [
  'esg-analyst',
  'digital-marketing',
];

const getJobSimulationsPaginated = async function (params) {
  try {
    const { search, page, limit } = params;
    const fields = params.fields || ['name', 'description', 'logo'];
    const query = {
      ...(search ? { name: { $regex: search, $options: 'i' } } : {}),
      jobSimulationId: { $in: LIST_JS_IDS },
    };

    const jobSimulations = await JobSimulation.find(query, fields)
      .skip((page - 1) * limit)
      .limit(limit)
      .lean()
      .exec();

    return jobSimulations;
  } catch (error) {
    throw new Error(`Failed to retrieve job simulations: ${error.message}`);
  }
};

const getUserJobSimulationsInfo = async function (params) {
  try {
    params.fields = ['jobSimulationId', 'name', 'description', 'logo', 'companyName', 'agentId', 'banner'];

    const jobSimulations = await getJobSimulationsPaginated(params);

    return jobSimulations;
  } catch (error) {
    throw new Error(`Failed to retrieve job simulations: ${error.message}`);
  }
};

const getJobSimulations = async function (params) {
  try {
    const { search, page, limit } = params;
    // const fields = params.fields || ['name', 'description', 'logo'];
    const query = {
      ...(search ? { name: { $regex: search, $options: 'i' } } : {}),
      jobSimulationId: { $in: LIST_JS_IDS },
    };

    const jobSimulations = await getJobSimulationsPaginated(params);

    const total = await JobSimulation.countDocuments(query).exec();

    return {
      data: jobSimulations,
      meta: {
        total,
        page,
        limit,
      }
    };
  } catch (error) {
    throw new Error(`Failed to retrieve job simulations: ${error.message}`);
  }
};

const getPublicJobSimulations = async function (params) {
  try {
    const { search, page, limit } = params;
    const fields = params.fields || ['name', 'description', 'banner'];
    const query = {
      ...(search ? { name: { $regex: search, $options: 'i' } } : {}),
      jobSimulationId: { $in: LIST_JS_IDS },
    };

    const jobSimulations = await JobSimulation.find(query, fields)
      .skip((page - 1) * limit)
      .limit(limit)
      .lean()
      .exec();

    const total = await JobSimulation.countDocuments(query).exec();

    const mapped = jobSimulations.map(({ banner, ...rest }) => ({
      ...rest,
      logo: banner,
    }));

    return {
      data: mapped,
      meta: {
        total,
        page,
        limit,
      }
    };
  } catch (error) {
    throw new Error(`Failed to retrieve job simulations: ${error.message}`);
  }
};

const getJobSimulation = async function (jobSimulationId) {
  try {
    let jobSimulation = await JobSimulation.findOne({ jobSimulationId }).lean().exec();
    return jobSimulation;
  } catch (error) {
    throw new Error(`Failed to retrieve job simulations: ${error.message}`);
  }
};

const updateLogo = async function (jobSimulationId, logo) {
  try {
    const jobSimulation = await JobSimulation.findOneAndUpdate(
      { jobSimulationId },
      { logo },
      { new: true },
    ).lean();
    return jobSimulation;
  } catch (error) {
    throw new Error(`Failed to update job simulation logo: ${error.message}`);
  }
};

const updateCredentials = async function (jobSimulationId, username, password) {
  try {
    const jobSimulation = await JobSimulation.findOneAndUpdate(
      { jobSimulationId },
      {
        credentials: {
          username,
          password
        }
      },
      { new: true },
    ).lean();

    return jobSimulation;
  } catch (error) {
    throw new Error(`Failed to update job simulation logo: ${error.message}`);
  }
};

module.exports = {
  model: JobSimulation,
  getPublicJobSimulations,
  getJobSimulations,
  getJobSimulation,
  getUserJobSimulationsInfo,
  updateLogo,
  updateCredentials,
};
