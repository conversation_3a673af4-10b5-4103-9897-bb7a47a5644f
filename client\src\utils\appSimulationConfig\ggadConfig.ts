import { AppSimulationScreen } from '~/common';
import { CalculationConfig } from '~/components/JobSimulation/AppSimulation/CalculationEngine';

const countries = [
	"Aruba",
	"Afghanistan",
	"Angola",
	"Anguilla",
	"Åland Islands",
	"Albania",
	"Andorra",
	"United Arab Emirates",
	"Argentina",
	"Armenia",
	"American Samoa",
	"Antarctica",
	"French Southern Territories",
	"Antigua and Barbuda",
	"Australia",
	"Austria",
	"Azerbaijan",
	"Burundi",
	"Belgium",
	"Benin",
	"Bonaire, Sint Eustatius and Saba",
	"Burkina Faso",
	"Bangladesh",
	"Bulgaria",
	"Bahrain",
	"Bahamas",
	"Bosnia and Herzegovina",
	"Saint Barthélemy",
	"Belarus",
	"Belize",
	"Bermuda",
	"Bolivia, Plurinational State of",
	"Brazil",
	"Barbados",
	"Brunei Darussalam",
	"Bhutan",
	"Bouvet Island",
	"Botswana",
	"Central African Republic",
	"Canada",
	"Cocos (Keeling) Islands",
	"Switzerland",
	"Chile",
	"China",
	"Côte d'Ivoire",
	"Cameroon",
	"Congo, The Democratic Republic of the",
	"Congo",
	"Cook Islands",
	"Colombia",
	"Comoros",
	"Cabo Verde",
	"Costa Rica",
	"Cuba",
	"Curaçao",
	"Christmas Island",
	"Cayman Islands",
	"Cyprus",
	"Czechia",
	"Germany",
	"Djibouti",
	"Dominica",
	"Denmark",
	"Dominican Republic",
	"Algeria",
	"Ecuador",
	"Egypt",
	"Eritrea",
	"Western Sahara",
	"Spain",
	"Estonia",
	"Ethiopia",
	"Finland",
	"Fiji",
	"Falkland Islands (Malvinas)",
	"France",
	"Faroe Islands",
	"Micronesia, Federated States of",
	"Gabon",
	"United Kingdom",
	"Georgia",
	"Guernsey",
	"Ghana",
	"Gibraltar",
	"Guinea",
	"Guadeloupe",
	"Gambia",
	"Guinea-Bissau",
	"Equatorial Guinea",
	"Greece",
	"Grenada",
	"Greenland",
	"Guatemala",
	"French Guiana",
	"Guam",
	"Guyana",
	"Hong Kong",
	"Heard Island and McDonald Islands",
	"Honduras",
	"Croatia",
	"Haiti",
	"Hungary",
	"Indonesia",
	"Isle of Man",
	"India",
	"British Indian Ocean Territory",
	"Ireland",
	"Iran, Islamic Republic of",
	"Iraq",
	"Iceland",
	"Israel",
	"Italy",
	"Jamaica",
	"Jersey",
	"Jordan",
	"Japan",
	"Kazakhstan",
	"Kenya",
	"Kyrgyzstan",
	"Cambodia",
	"Kiribati",
	"Saint Kitts and Nevis",
	"Korea, Republic of",
	"Kuwait",
	"Lao People's Democratic Republic",
	"Lebanon",
	"Liberia",
	"Libya",
	"Saint Lucia",
	"Liechtenstein",
	"Sri Lanka",
	"Lesotho",
	"Lithuania",
	"Luxembourg",
	"Latvia",
	"Macao",
	"Saint Martin (French part)",
	"Morocco",
	"Monaco",
	"Moldova, Republic of",
	"Madagascar",
	"Maldives",
	"Mexico",
	"Marshall Islands",
	"North Macedonia",
	"Mali",
	"Malta",
	"Myanmar",
	"Montenegro",
	"Mongolia",
	"Northern Mariana Islands",
	"Mozambique",
	"Mauritania",
	"Montserrat",
	"Martinique",
	"Mauritius",
	"Malawi",
	"Malaysia",
	"Mayotte",
	"Namibia",
	"New Caledonia",
	"Niger",
	"Norfolk Island",
	"Nigeria",
	"Nicaragua",
	"Niue",
	"Netherlands",
	"Norway",
	"Nepal",
	"Nauru",
	"New Zealand",
	"Oman",
	"Pakistan",
	"Panama",
	"Pitcairn",
	"Peru",
	"Philippines",
	"Palau",
	"Papua New Guinea",
	"Poland",
	"Puerto Rico",
	"Korea, Democratic People's Republic of",
	"Portugal",
	"Paraguay",
	"Palestine, State of",
	"French Polynesia",
	"Qatar",
	"Réunion",
	"Romania",
	"Russian Federation",
	"Rwanda",
	"Saudi Arabia",
	"Sudan",
	"Senegal",
	"Singapore",
	"South Georgia and the South Sandwich Islands",
	"Saint Helena, Ascension and Tristan da Cunha",
	"Svalbard and Jan Mayen",
	"Solomon Islands",
	"Sierra Leone",
	"El Salvador",
	"San Marino",
	"Somalia",
	"Saint Pierre and Miquelon",
	"Serbia",
	"South Sudan",
	"Sao Tome and Principe",
	"Suriname",
	"Slovakia",
	"Slovenia",
	"Sweden",
	"Eswatini",
	"Sint Maarten (Dutch part)",
	"Seychelles",
	"Syrian Arab Republic",
	"Turks and Caicos Islands",
	"Chad",
	"Togo",
	"Thailand",
	"Tajikistan",
	"Tokelau",
	"Turkmenistan",
	"Timor-Leste",
	"Tonga",
	"Trinidad and Tobago",
	"Tunisia",
	"Turkey",
	"Tuvalu",
	"Taiwan, Province of China",
	"Tanzania, United Republic of",
	"Uganda",
	"Ukraine",
	"United States Minor Outlying Islands",
	"Uruguay",
	"United States",
	"Uzbekistan",
	"Holy See (Vatican City State)",
	"Saint Vincent and the Grenadines",
	"Venezuela, Bolivarian Republic of",
	"Virgin Islands, British",
	"Virgin Islands, U.S.",
	"Viet Nam",
	"Vanuatu",
	"Wallis and Futuna",
	"Samoa",
	"Yemen",
	"South Africa",
	"Zambia",
	"Zimbabwe"
];

const languages = [
	'Arabic',
	'Bengali',
	'Bulgarian',
	'Catalan',
	'Chinese (simplified)',
	'Chinese (traditional)',
	'Croatian',
	'Czech',
	'Danish',
	'Dutch',
	'English',
	'Estonian',
	'Filipino',
	'Finnish',
	'French',
	'German',
	'Greek',
	'Gujarati',
	'Hebrew',
	'Hindi',
	'Hungarian',
	'Icelandic',
	'Indonesian',
	'Italian',
	'Japanese',
	'Kannada',
	'Korean',
	'Latvian',
	'Lithuanian',
	'Malay',
	'Malayalam',
	'Marathi',
	'Norwegian',
	'Persian',
	'Polish',
	'Portuguese',
	'Punjabi',
	'Romanian',
	'Russian',
	'Serbian',
	'Slovak',
	'Slovenian',
	'Spanish',
	'Swedish',
	'Tamil',
	'Telugu',
	'Thai',
	'Turkish',
	'Ukrainian',
	'Urdu',
	'Vietnamese',
]
export const ggAdCalculationConfig: CalculationConfig = {
	rules: {
		estimated_audience_size: {
			id: 'estimated_audience_size',
			type: 'range',
			inputs: [
				'daily_campaign_budget',
				'audience_language',
				'audience_location',
			],
			baseValue: 3000000,
			multipliers: {
				daily_campaign_budget: { type: 'linear', factor: 0.5 },
				audience_location: { type: 'array_length', factor: 1.2 },
				audience_language: { type: 'array_length', factor: 1.1 },
				audience_location_language_bonus: {
					type: 'conditional',
					conditions: [
						// Japan + Japanese = high compatibility
						{
							condition:
								'audience_location && audience_language && audience_location.includes("Japan") && audience_language.includes("Japanese")',
							value: 1.3,
						},
						// Korea + Korean = high compatibility
						{
							condition:
								'audience_location && audience_language && audience_location.includes("Republic of Korea") && audience_language.includes("Korean")',
							value: 1.3,
						},
						// France + French = high compatibility
						{
							condition:
								'audience_location && audience_language && audience_location.includes("France") && audience_language.includes("French")',
							value: 1.3,
						},
						// Thailand + Thai = high compatibility
						{
							condition:
								'audience_location && audience_language && audience_location.includes("Thailand") && audience_language.includes("Thai")',
							value: 1.3,
						},
						// Vietnam + Vietnamese = high compatibility
						{
							condition:
								'audience_location && audience_language && audience_location.includes("Vietnam") && audience_language.includes("Vietnamese")',
							value: 1.3,
						},
						// China + Chinese = high compatibility
						{
							condition:
								'audience_location && audience_language && audience_location.includes("China") && audience_language.includes("Chinese")',
							value: 1.3,
						},
						// Indonesia + Indonesian = high compatibility
						{
							condition:
								'audience_location && audience_language && audience_location.includes("Indonesia") && audience_language.includes("Indonesian")',
							value: 1.3,
						},
						// English-speaking countries + English = good compatibility
						{
							condition:
								'audience_location && audience_language && (audience_location.includes("Singapore") || audience_location.includes("Hong Kong") || audience_location.includes("Australia")) && audience_language.includes("English")',
							value: 1.2,
						},
						// Default case - no special bonus
						{ condition: 'true', value: 1.0 },
					],
				},
			},
			formatters: [{ type: 'number' }],
			rangeMultiplier: 1.1,
		},
	},
	dependencies: {
		daily_campaign_budget: [
			'estimated_audience_size',
		],
		total_campaign_budget: [
		],
		audience_location: [
			'estimated_audience_size',
		],
		audience_language: ['estimated_audience_size'],
		audience_location_language_bonus: ['estimated_audience_size'],
		audience_age: [
			'estimated_audience_size',
		],
		gender: [
			'estimated_audience_size',
		],
		operation_system: ['estimated_audience_size'],
	},
	defaultValues: {
		daily_campaign_budget: 1,
		total_campaign_budget: 30,
		audience_location: ['Singapore'],
		audience_language: ['English'],
	},
};

const biddingDropdownOptions = [
	{
		label: 'Conversions',
		screenId: '005_1',
		dataContextId: 'bidding',
		saveToSelections: true,
	},
	{
		label: 'Conversion value',
		screenId: '005_2',
		dataContextId: 'bidding',
		saveToSelections: true,
	},
	{
		label: 'Clicks',
		screenId: '005_3',
		dataContextId: 'bidding',
		saveToSelections: true,
	},
	{
		label: 'Impression share',
		screenId: '005_4',
		dataContextId: 'bidding',
		saveToSelections: true,
	},
];

const adsToAppearDropdownOptions = [
	{
		label: 'Anywhere on results page',
		screenId: '005_2',
		dataContextId: 'bidding',
		saveToSelections: true,
	},
	{
		label: 'Top of results page',
		screenId: '005_3',
		dataContextId: 'bidding',
		saveToSelections: true,
	},
	{
		label: 'Absolute top of results page',
		screenId: '005_4',
		dataContextId: 'bidding',
		saveToSelections: true,
	},
];

export const ggAdScreens: AppSimulationScreen[] = [
	{
		id: '001',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/001.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 0.2777777777777778,
				y1: 1.006821789682937,
				x2: 2.857142857142857,
				y2: 8.146103571071036,
				title: "Create Campaign",
				action: { type: 'nextScreen' },
			},
			{
				x1: 28.174603174603174,
				y1: 6.956223274173019,
				x2: 35,
				y2: 11.532685954550006,
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				title: "Create Campaign",
				action: { type: 'nextScreen' },
			},
			{
				x1: 31.26984126984127,
				y1: 33.774294581182154,
				x2: 38.01587301587301,
				y2: 37.06934771105359,
				title: "Create Campaign",
				action: { type: 'nextScreen' },
			}
		],
	},
	{
		id: '002',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/002.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 7.222222222222221,
				y1: 27.006307510466492,
				x2: 27.380952380952383,
				y2: 52.45727793917837,
				title: "Objective-Sales",
				action: { type: 'nextScreen' },
			},
			{
				x1: 28.76984126984127,
				y1: 27.006307510466492,
				x2: 49.047619047619044,
				y2: 52.45727793917837,
				title: "Objective-Leads",
				action: { type: 'nextScreen' },
			},
			{
				x1: 50.357142857142854,
				y1: 27.006307510466492,
				x2: 70.55555555555556,
				y2: 52.38658079909862,
				title: "Objective-Traffic",
				action: { type: 'nextScreen' },
			},
			{
				x1: 71.94444444444444,
				y1: 27.006307510466492,
				x2: 92.14285714285714,
				y2: 52.45727793917837,
				title: "Objective-Promotion",
				action: { type: 'nextScreen' },
			},
			{
				x1: 7.182539682539682,
				y1: 54.860980701890036,
				x2: 27.420634920634924,
				y2: 80.38264827068168,
				title: "Objective-AC",
				action: { type: 'nextScreen' },
			},
			{
				x1: 28.76984126984127,
				y1: 54.931677841969794,
				x2: 49.007936507936506,
				y2: 80.45334541076143,
				title: "Objective-Shop",
				action: { type: 'nextScreen' },
			},
			{
				x1: 50.317460317460316,
				y1: 54.860980701890036,
				x2: 70.63492063492063,
				y2: 80.45334541076143,
				title: "Objective-Custom",
				action: { type: 'nextScreen' },
			},
			{
				x1: 81.62698412698413,
				y1: 87.80584797905597,
				x2: 87.06349206349206,
				y2: 93.17883062511737,
				title: "Cancel",
				action: { type: 'nextScreen', screenId: '001' },
			}
		],
	},
	{
		id: '003',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/003.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 6.428571428571428,
				y1: 18.271997492078775,
				x2: 27.063492063492063,
				y2: 48.15208750853701,
				title: "Search-Type",
				action: { type: 'nextScreen' }
			},
			{
				x1: 28.452380952380953,
				y1: 18.271997492078775,
				x2: 49.166666666666664,
				y2: 48.15208750853701,
				title: "Performance-Type",
				action: { type: 'nextScreen' }
			},
			{
				x1: 50.476190476190474,
				y1: 18.271997492078775,
				x2: 71.19047619047619,
				y2: 48.22374240066281,
				title: "Demand-Type",
				action: { type: 'nextScreen' }
			},
			{
				x1: 72.57936507936508,
				y1: 18.271997492078775,
				x2: 93.21428571428572,
				y2: 48.22374240066281,
				title: "Display-Type",
				action: { type: 'nextScreen' }
			},
			{
				x1: 6.428571428571428,
				y1: 50.73166362506578,
				x2: 27.023809523809522,
				y2: 80.68340853364981,
				title: "Shopping-Type",
				action: { type: 'nextScreen' }
			},
			{
				x1: 28.452380952380953,
				y1: 50.66000873293998,
				x2: 49.166666666666664,
				y2: 80.68340853364981,
				title: "Video-Type",
				action: { type: 'nextScreen' }
			},
			{
				x1: 50.476190476190474,
				y1: 50.58835384081418,
				x2: 71.19047619047619,
				y2: 80.68340853364981,
				title: "App-Type",
				action: { type: 'nextScreen' }
			},
			{
				x1: 82.26190476190476,
				y1: 88.49379177536191,
				x2: 87.93650793650794,
				y2: 94.01121846904844,
				title: "Cancel",
				action: { type: 'nextScreen', screenId: '002' }
			}
		],
	},
	{
		id: '004',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/004.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 5.893987341772152,
				y1: 38.64161784840527,
				x2: 32.99050632911392,
				y2: 53.72477723161179,
				title: "Input Campaign Name",
				action: {
					type: 'inputText',
					inputType: 'input',
					dataContextId: 'campainName',
					dataContextLabel: "Campain Name",
					saveToSelections: true,
				},
			},
			{
				x1: 82.91139240506328,
				y1: 78.71972706663973,
				x2: 88.53174603174602,
				y2: 89.77753760244978,
				title: "Cancel",
				action: { type: 'nextScreen', screenId: '003' }
			},
			{
				x1: 89.04761904761904,
				y1: 78.53733225254436,
				x2: 95.63492063492063,
				y2: 90.0657479960371,
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				title: "Continue",
				action: { type: 'nextScreen' }
			}
		],
		placeholders: [
			{
				id: 'text',
				type: 'text',
				dataId: 'campainName',
				x1: 6.666666666666667,
				y1: 41.21408628298658,
				x2: 32.420634920634924,
				y2: 51.445555255336394,
				title: 'Text placeholder',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
		],
	},
	{
		id: '005_1',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/005_1.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 26.78006329113924,
				y1: 32.266845586120965,
				x2: 34.84126984126984,
				y2: 41.47692307692308,
				title: "Conversions",
				action: {
					type: 'dropdown',
					dropdownOptions: biddingDropdownOptions,
				},
			},
			{
				x1: 26.785714285714285,
				y1: 47.38461538461539,
				x2: 27.936507936507937,
				y2: 50.95384615384615,
				title: "Conversion Checkbox",
				action: { type: 'nextScreen', screenId: '005_5' }
			},
			{
				x1: 84.44444444444444,
				y1: 82.58461538461539,
				x2: 89.52380952380953,
				y2: 89.84615384615384,
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				title: "Conversion Next",
				action: { type: 'nextScreen', screenId: '006' }
			}
		]
	},
	{
		id: '005_2',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/005_2.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 26.740506329113924,
				y1: 32.37259018896736,
				x2: 36.7063492063492,
				y2: 41.78841550981331,
				title: "Conversion Value",
				action: {
					type: 'dropdown',
					dropdownOptions: biddingDropdownOptions,
				},
			},
			{
				x1: 26.785714285714285,
				y1: 47.67065581617999,
				x2: 27.81746031746032,
				y2: 50.97941598851124,
				title: "Conversion Value Checkbox",
				action: { type: 'nextScreen', screenId: '005_6' }
			},
			{
				x1: 84.4047619047619,
				y1: 82.71900430828147,
				x2: 89.44444444444444,
				y2: 89.82671134514122,
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				title: "Conversion Value Next",
				action: { type: 'nextScreen', screenId: '006' }
			}
		]
	},
	{
		id: '005_3',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/005_3.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 26.78006329113924,
				y1: 32.2610435585655,
				x2: 32.34126984126984,
				y2: 41.8752774507344,
				title: "Clicks",
				action: {
					type: 'dropdown',
					dropdownOptions: biddingDropdownOptions,
				},
			},
			{
				x1: 26.825396825396826,
				y1: 47.68099438343209,
				x2: 27.936507936507937,
				y2: 51.01619347242864,
				title: "Click Checkbox",
				action: { type: 'nextScreen', screenId: '005_7' },
			},
			{
				x1: 84.52380952380952,
				y1: 83.13292544054352,
				x2: 89.56349206349206,
				y2: 90.17390129509178,
				title: "Click Next",
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				action: { type: 'nextScreen', screenId: '006' },
			}
		]
	},
	{
		id: '005_4',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/005_4.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 26.666666666666668,
				y1: 19.593905056832178,
				x2: 36.74603174603175,
				y2: 25.524627277099388,
				title: "Impression Share",
				action: {
					type: 'dropdown',
					dropdownOptions: biddingDropdownOptions,
				},
			},
			{
				x1: 26.706349206349206,
				y1: 31.4553494973666,
				x2: 40.15873015873016,
				y2: 37.23592685129793,
				title: "Appear",
				action: {
					type: 'dropdown',
					dropdownOptions: adsToAppearDropdownOptions,
				},
			},
			{
				x1: 26.666666666666668,
				y1: 43.76722853690866,
				x2: 37.817460317460316,
				y2: 49.62287832400793,
				title: "Impression Share Percent",
				action: {
					type: 'inputText',
					inputType: 'input',
					dataContextId: 'ISPercent',
					dataContextLabel: "Percent (%) impression share to target",
					saveToSelections: true,
				},
			},
			{
				x1: 26.666666666666668,
				y1: 56.60461460862629,
				x2: 37.77777777777778,
				y2: 62.535336828893506,
				title: "Max CPC Bid Limit",
				action: {
					type: 'inputText',
					inputType: 'input',
					dataContextId: 'maxCPCBidLimit',
					dataContextLabel: "Maximum CPC bid limit",
					saveToSelections: true,
				},
			},
			{
				x1: 84.48412698412699,
				y1: 93.31503442774864,
				x2: 89.48412698412699,
				y2: 97.66923555148914,
				title: "Impression Share Next",
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				action: { type: 'nextScreen', screenId: '006' },
			},
		],
		placeholders: [
			{
				id: 'text',
				type: 'number',
				dataId: 'ISPercent',
				x1: 26.666666666666668,
				y1: 43.76722853690866,
				x2: 37.817460317460316,
				y2: 49.62287832400793,
				title: 'Text placeholder',
				style: {
					paddingLeft: '1rem',
				},
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
			{
				id: 'text',
				type: 'number',
				dataId: 'maxCPCBidLimit',
				x1: 26.666666666666668,
				y1: 56.60461460862629,
				x2: 37.77777777777778,
				y2: 62.535336828893506,
				title: 'Text placeholder',
				style: {
					paddingLeft: '2.25rem',
				},
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
		],
	},
	{
		id: '005_5',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/005_5.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 26.785714285714285,
				y1: 27.0059724746819,
				x2: 34.84126984126984,
				y2: 35.00389509218385,
				title: "Conversions Dropdown",
				action: {
					type: 'dropdown',
					dropdownOptions: biddingDropdownOptions,
				},
			},
			{
				x1: 26.746031746031747,
				y1: 50.06491820306414,
				x2: 37.857142857142854,
				y2: 58.06284082056609,
				title: "Target CPA Input",
				action: {
					type: 'inputText',
					inputType: 'input',
					dataContextId: 'targetCPA',
					dataContextLabel: "Target CPA",
					saveToSelections: true,
				},
			},
			{
				x1: 26.746031746031747,
				y1: 39.885743962607116,
				x2: 27.936507936507937,
				y2: 43.001817709685795,
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				title: "Checkbox",
				action: { type: 'nextScreen', screenId: '005_1' },
			},
			{
				x1: 84.4047619047619,
				y1: 86.31524279407947,
				x2: 89.44444444444444,
				y2: 92.4435211633342,
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				title: "Next",
				action: { type: 'nextScreen', screenId: '006' },
			}
		],
		placeholders: [
			{
				id: 'targetCPA',
				type: 'number',
				dataId: 'targetCPA',
				x1: 29.08730158730159,
				y1: 51.51908595170086,
				x2: 37.26190476190476,
				y2: 56.712542196832,
				title: 'Target CPA',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
		],
	},
	{
		id: '005_6',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/005_6.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 26.706349206349206,
				y1: 26.92671471902417,
				x2: 36.7063492063492,
				y2: 35.06734940151985,
				title: "Conversion Value Dropdown",
				action: {
					type: 'dropdown',
					dropdownOptions: biddingDropdownOptions,
				},
			},
			{
				x1: 26.706349206349206,
				y1: 39.86823652196602,
				x2: 27.8968253968254,
				y2: 42.999249861387426,
				title: "Checkbox",
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				action: { type: 'nextScreen', screenId: '005_2' },
			},
			{
				x1: 26.706349206349206,
				y1: 50.096213430742644,
				x2: 32.817460317460316,
				y2: 58.02811389061022,
				title: "Target ROAS",
				action: {
					type: 'inputText',
					inputType: 'input',
					dataContextLabel: "Target ROAS",
					dataContextId: 'targetROAS',
					saveToSelections: true,
				},
			},
			{
				x1: 84.4047619047619,
				y1: 86.52033527934509,
				x2: 89.44444444444444,
				y2: 92.78236195818793,
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				title: "Next",
				action: { type: 'nextScreen', screenId: '006' },
			}
		],
		placeholders: [
			{
				id: 'targetROAS',
				type: 'number',
				dataId: 'targetROAS',
				x1: 27.26190476190476,
				y1: 51.661720100453344,
				x2: 30.833333333333336,
				y2: 56.67134144352761,
				title: 'Target ROAS',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
		],
	},
	{
		id: '005_7',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/005_7.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 26.746031746031747,
				y1: 27.206645898234683,
				x2: 32.34126984126984,
				y2: 35.202492211838006,
				title: "Clicks Dropdown",
				action: {
					type: 'dropdown',
					dropdownOptions: biddingDropdownOptions,
				},
			},
			{
				x1: 26.746031746031747,
				y1: 40.083073727933545,
				x2: 27.936507936507937,
				y2: 43.30218068535825,
				title: "Checkbox",
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				action: { type: 'nextScreen', screenId: '005_3' },
			},
			{
				x1: 26.785714285714285,
				y1: 50.25960539979232,
				x2: 37.857142857142854,
				y2: 58.25545171339564,
				title: "Maximum CPC Bid Limit Clicks",
				action: {
					type: 'inputText',
					inputType: 'input',
					dataContextLabel: "Maximum CPC bid limit",
					dataContextId: 'maxCPCClicks',
					saveToSelections: true,
				},
			},
			{
				x1: 84.56349206349206,
				y1: 86.6043613707165,
				x2: 89.56349206349206,
				y2: 92.73104880581516,
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				title: "Next",
				action: { type: 'nextScreen', screenId: '006' },
			}
		],
		placeholders: [
			{
				id: 'maxCPCClicks',
				type: 'number',
				dataId: 'maxCPCClicks',
				x1: 28.69047619047619,
				y1: 51.298026998961575,
				x2: 37.22222222222222,
				y2: 57.3208722741433,
				title: 'Maximum CPC bid limit',
				style: {
					fontSize: '0.875rem',
				},
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
		],
	},
	{
		id: '006',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/006.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 84.48412698412699,
				y1: 90.79561348863997,
				x2: 89.48412698412699,
				y2: 95.23384148849892,
				title: "Next",
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				action: {
					type: 'nextScreen',
					screenId: '007_1'
				},
			},
			{
				x1: 0.992063492063492,
				y1: 8.124213965843511,
				x2: 10.634920634920634,
				y2: 12.186320948765266,
				"title": "Bidding",
				action: {
					type: 'nextScreen',
					screenId: '005_1'
				},
			},
			{
				x1: 26.904761904761905,
				y1: 21.288449558645496,
				x2: 28.095238095238095,
				y2: 23.54517566026869,
				title: 'Search partners',
				action: {
					type: 'checkbox',
					dataContext: 'Search partners',
					dataContextId: 'value_network',
					dataContextLabel: 'Value Network',
					saveToSelections: true,
				},
			},
			{
				x1: 26.904761904761905,
				y1: 39.49270677840595,
				x2: 28.095238095238095,
				y2: 41.89988128680403,
				title: 'Display Network',
				action: {
					type: 'checkbox',
					dataContext: 'Display Network',
					dataContextId: 'value_network',
					dataContextLabel: 'Value Network',
					saveToSelections: true,
				},
			},
		],
		placeholders: [
			{
				id: 'search_partners',
				x1: 26.904761904761905,
				y1: 21.288449558645496,
				x2: 28.095238095238095,
				y2: 23.54517566026869,
				type: 'checkbox',
				dataId: 'value_network',
				checkboxValue: 'Search partners',
				style: {
					// backgroundColor: '#1c73e8',
					color: '#1c73e8'
				}
			},
			{
				id: 'display_network',
				x1: 26.904761904761905,
				y1: 39.49270677840595,
				x2: 28.095238095238095,
				y2: 41.89988128680403,
				type: 'checkbox',
				dataId: 'value_network',
				checkboxValue: 'Display Network',
				style: {
					// backgroundColor: '#1c73e8',
					color: '#1c73e8'
				}
			}
		]
	},
	{
		id: '007_1',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/007_1.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 20.992063492063494,
				y1: 38.7677658095081,
				x2: 32.77777777777778,
				y2: 41.70928889622175,
				title: "Enter Location",
				action: {
					type: 'nextScreen',
					screenId: '007_3'
				},
			},
			{
				x1: 20.992063492063494,
				y1: 34.393193013882666,
				x2: 32.142857142857146,
				y2: 38.23980012727744,
				"title": "One Location",
				action: {
					type: 'nextScreen',
					screenId: '007_2'
				},
			},
			{
				x1: 0.873015873015873,
				y1: 8.2211799090202,
				x2: 12.698412698412698,
				y2: 12.143210691305065,
				"title": "Bidding",
				action: {
					type: 'nextScreen',
					screenId: '005_1'
				},
			},
			{
				x1: 3.4126984126984126,
				y1: 19.45930657364414,
				x2: 7.6984126984126995,
				y2: 22.928795342588444,
				"title": "Network",
				action: {
					type: 'nextScreen',
					screenId: '006'
				},
			},
			{
				x1: 78.73015873015873,
				y1: 83.94654347467416,
				x2: 83.80952380952381,
				y2: 88.47196360807976,
				title: "Next",
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				action: {
					type: 'nextScreen',
					screenId: '008'
				},
			}
		],
		placeholders: [
			{
				id: "value_network",
				dataId: 'value_network',
				type: 'text',
				dataDisplayType: 'text',
				x1: 34.007936507936506,
				y1: 12.067787022414972,
				x2: 80.71428571428572,
				y2: 16.66863082471068,
				title: 'Location Input',
				style: {
					fontWeight: 'bold',
					backgroundColor: '#fff',
					color: '#7f7f7f'
				},
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
		],
	},
	{
		id: '007_2',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/007_2.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 20.793650793650794,
				y1: 32.2810251683437,
				x2: 35.55555555555556,
				y2: 36.78725964615818,
				"title": "All Location",
				action: {
					type: 'nextScreen',
					screenId: '007_1'
				},
			},
			{
				x1: 20.793650793650794,
				y1: 40.71997337225081,
				x2: 35.595238095238095,
				y2: 45.226207850065286,
				"title": "Another Location",
				action: {
					type: 'nextScreen',
					screenId: '007_3'
				},
			},
			{
				x1: 0.992063492063492,
				y1: 9.012468955628952,
				x2: 8.412698412698413,
				y2: 13.109045753642112,
				"title": "Bidding",
				action: {
					type: 'nextScreen',
					screenId: '005_1'
				},
			},
			{
				x1: 3.3333333333333335,
				y1: 21.220267813708173,
				x2: 10.119047619047619,
				y2: 24.989118467880278,
				"title": "Network",
				action: {
					type: 'nextScreen',
					screenId: '006'
				},
			},
			{
				x1: 78.69047619047619,
				y1: 89.3053741966869,
				x2: 83.73015873015873,
				y2: 94.22126635430268,
				"title": "Next",
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				action: {
					type: 'nextScreen',
					screenId: '008'
				},
			},
		],
		placeholders: [
			{
				id: "value_network",
				type: 'text',
				dataId: 'value_network',
				x1: 33.69047619047619,
				y1: 12.53552500192027,
				x2: 80.63492063492063,
				y2: 17.20562255165527,
				title: 'Location Input',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
				style: {
					fontWeight: 'bold',
					backgroundColor: '#fff',
					color: '#7f7f7f'
				},
			},
		],
	},
	{
		id: '007_3',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/007_3.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 20.992063492063494,
				y1: 30.380694041717334,
				x2: 35.3968253968254,
				y2: 34.42147600785708,
				"title": "All Location",
				action: {
					type: 'nextScreen',
					screenId: '007_1'
				},
			},
			{
				x1: 20.992063492063494,
				y1: 34.79562248620335,
				x2: 35.43650793650794,
				y2: 37.93845290431204,
				"title": "One Location",
				action: {
					type: 'nextScreen',
					screenId: '007_2'
				},
			},
			{
				x1: 0.9126984126984128,
				y1: 8.380881114956505,
				x2: 7.4603174603174605,
				y2: 12.272004489757741,
				"title": "Bidding",
				action: {
					type: 'nextScreen',
					screenId: '005_1'
				},
			},
			{
				x1: 3.4126984126984126,
				y1: 19.53044616967543,
				x2: 9.325396825396826,
				y2: 23.271910953138153,
				"title": "Network",
				action: {
					type: 'nextScreen',
					screenId: '006'
				},
			},
			{
				x1: 22.817460317460316,
				y1: 44.29894303619867,
				x2: 41.70634920634921,
				y2: 50.13562809840052,
				title: "Enter Location Input",
				action: {
					type: 'modal',
					modalConfig: {
						title: 'Loctions',
						description: 'Select locations',
						inputs: [
							{
								id: 'location',
								type: 'multiSelect',
								label: 'Locations',
								dataId: 'audience_location',
								options: countries,
								required: true,
								minSelections: 1,
								defaultValue: ['All countries and territories'],
							},
						],
					},
				},
			},
			{
				x1: 78.76984126984127,
				y1: 94.21008324759144,
				x2: 83.88888888888889,
				y2: 98.62501169207745,
				title: "Next",
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				action: { type: 'nextScreen', screenId: '008' },
			}
		],
		placeholders: [

			{
				id: "location",
				type: 'text',
				dataId: 'audience_location',
				x1: 22.936507936507937,
				y1: 50.43494528107754,
				x2: 81.62698412698413,
				y2: 53.72743429052475,
				title: "Location List",
				style: {
					backgroundColor: '#fff',
					color: '#7f7f7f',
					fontWeight: 500,
				},
				responsiveConfig: {
					baseFontSize: 1,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
			{
				id: 'value_network',
				type: 'text',
				dataId: 'value_network',
				x1: 34.007936507936506,
				y1: 12.067787022414972,
				x2: 80.71428571428572,
				y2: 16.66863082471068,
				title: 'Location Input',
				responsiveConfig: {
					baseFontSize: 1,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
				style: {
					fontWeight: 500,
					backgroundColor: '#fff',
					color: '#7f7f7f'
				},
			},
		],
	},
	{
		id: '008',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/008.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 26.58730158730159,
				y1: 36.04201740450051,
				x2: 51.746031746031754,
				y2: 41.6569843264648,
				title: "Languages Input",
				action: {
					type: 'modal',
					modalConfig: {
						title: 'Language',
						description: 'Select languages',
						inputs: [
							{
								id: 'language',
								type: 'multiSelect',
								label: 'Languages',
								dataId: 'audience_language',
								options: languages,
								required: true,
								minSelections: 1,
								defaultValue: ['English'],
							},
						],
					},
				},
			},
			{
				x1: 84.28571428571429,
				y1: 67.68311478908306,
				x2: 89.32539682539684,
				y2: 72.15991274037891,
				title: "Next",
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				action: {
					type: 'nextScreen',
					screenId: '010_1'
				},
			},
			{
				x1: 0.6746031746031746,
				y1: 8.043060726056956,
				x2: 9.722222222222223,
				y2: 12.064591089085434,
				"title": "Bidding",
				action: {
					type: 'nextScreen',
					screenId: '005_1'
				},
			},
			{
				x1: 3.1746031746031744,
				y1: 19.19711663860764,
				x2: 8.055555555555555,
				y2: 22.99101320750243,
				"title": "Network",
				action: {
					type: 'nextScreen',
					screenId: '006'
				},
			},
			{
				x1: 3.134920634920635,
				y1: 24.280938040926657,
				x2: 8.65079365079365,
				y2: 27.771322884309868,
				"title": "Locations",
				action: {
					type: 'nextScreen',
					screenId: '007_3'
				},
			}
		],
		placeholders: [
			{
				id: 'location',
				type: 'text',
				dataId: 'audience_location',
				x1: 39.56349206349206,
				y1: 17.527802148293933,
				x2: 85.9126984126984,
				y2: 22.308111825101367,
				title: 'Location Input',
				style: {
					backgroundColor: '#fff',
					fontWeight: 500,
					color: '#7f7f7f',
				},
				responsiveConfig: {
					baseFontSize: 1,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
			{
				id: 'language',
				type: 'text',
				dataDisplayType: 'tag',
				dataId: 'audience_language',
				x1: 26.58730158730159,
				y1: 44.160956061935366,
				x2: 87.38095238095238,
				y2: 49.32065539563228,
				title: 'Location Input',
				style: {
					backgroundColor: '#fff',
				},
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
			{
				id: "value_network",
				type: 'text',
				dataId: 'value_network',
				x1: 39.44444444444444,
				y1: 12.216346951841226,
				x2: 85.9126984126984,
				y2: 16.237877314869703,
				title: 'Location Input',
				responsiveConfig: {
					baseFontSize: 1,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
				style: {
					fontWeight: 500,
					backgroundColor: '#fff',
					color: '#7f7f7f',
				},
			},
		],
	},
	{
		id: '010_1',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/010_1.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 29.325396825396826,
				y1: 31.7254528122021,
				x2: 45.67460317460317,
				y2: 37.611170302248645,
				title: "Url Input",
				action: {
					type: 'inputText',
					inputType: 'input',
					dataContextId: 'urlInput',
					dataContextLabel: "Final Url",
					saveToSelections: true,
				},
			},
			{
				x1: 30.198412698412696,
				y1: 54.76588347445747,
				x2: 37.46031746031746,
				y2: 60.723378007065556,
				title: "Display path",
				action: {
					type: 'inputText',
					inputType: 'input',
					dataContextLabel: "Display Path",
					dataContextId: 'displayPath',
					saveToSelections: true,
				},
			},
			{
				x1: 43.25396825396825,
				y1: 68.2599674760276,
				x2: 45.27777777777778,
				y2: 71.13104917848932,
				title: "Headlines",
				action: {
					type: 'nextScreen',
					screenId: '010_2'
				},
			},
			{
				x1: 43.214285714285715,
				y1: 76.51432737060506,
				x2: 45.43650793650794,
				y2: 80.53384175405148,
				title: "Descriptions",
				action: {
					type: 'nextScreen',
					screenId: '010_3'
				},
			},
			{
				x1: 0.9523809523809524,
				y1: 8.469691022262097,
				x2: 7.579365079365079,
				y2: 12.202097235462345,
				"title": "Bidding",
				action: {
					type: 'nextScreen',
					screenId: '005_1'
				},
			},
			{
				x1: 0.873015873015873,
				y1: 13.996523299500927,
				x2: 11.785714285714285,
				y2: 18.15959176807043,
				"title": "Campaign Settings",
				action: {
					type: 'nextScreen',
					screenId: '008'
				},
			}
		],
		placeholders: [
			{
				id: 'urlInput',
				type: 'text',
				dataId: 'urlInput',
				x1: 29.92063492063492,
				y1: 32.51500028037908,
				x2: 45.15873015873016,
				y2: 36.89339987663321,
				style: {
					backgroundColor: '#fff',
				},
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
				title: 'Url Input',
			},
			{
				id: 'displayPath',
				type: 'text',
				dataId: 'displayPath',
				x1: 30.634920634920636,
				y1: 55.770762070319066,
				x2: 37.06349206349206,
				y2: 60.077384624011664,
				title: 'Display Input',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
			{
				id: 'viewDisplay',
				type: 'view',
				dataId: 'viewDisplay',
				x1: 55.952380952380956,
				y1: 43.8557730051029,
				x2: 74.68253968253968,
				y2: 88.35753939325969,
				title: 'View Display',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
		],
	},
	{
		id: '010_2',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/010_2.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 42.06349206349206,
				y1: 22.37905549934036,
				x2: 44.72222222222222,
				y2: 27.297529235459123,
				title: "Next Description",
				action: {
					type: 'nextScreen',
					screenId: '010_3'
				},
			},
			{
				x1: 26.746031746031747,
				y1: 32.13402840930924,
				x2: 44.88095238095238,
				y2: 39.51173901348738,
				title: "Headline1",
				action: {
					type: 'inputText',
					inputType: 'input',
					dataContextLabel: "Headline1",
					dataContextId: 'headline1',
					saveToSelections: true,
				},
			},
			{
				x1: 26.746031746031747,
				y1: 43.52849256465103,
				x2: 44.88095238095238,
				y2: 50.906203168829165,
				title: "Headline2",
				action: {
					type: 'inputText',
					inputType: 'input',
					dataContextLabel: "Headline2",
					dataContextId: 'headline2',
					saveToSelections: true,
				},
			},
			{
				x1: 26.746031746031747,
				y1: 54.840982157724184,
				x2: 44.84126984126984,
				y2: 62.21869276190232,
				title: "Headline3",
				action: {
					type: 'inputText',
					inputType: 'input',
					dataContextLabel: "Headline3",
					dataContextId: 'headline3',
					saveToSelections: true,
				},
			},
			{
				x1: 1.0317460317460316,
				y1: 11.312489593073149,
				x2: 9.325396825396826,
				y2: 16.312937891460557,
				"title": "Bidding",
				action: {
					type: 'nextScreen',
					screenId: '005_1'
				},
			},
			{
				x1: 1.0714285714285714,
				y1: 18.526251072713997,
				x2: 13.571428571428571,
				y2: 23.854597620175987,
				"title": "Campaign Settings",
				action: {
					type: 'nextScreen',
					screenId: '008'
				},
			}
		],
		placeholders: [
			{
				id: 'headline1',
				type: 'text',
				dataId: 'headline1',
				x1: 27.18253968253968,
				y1: 33.19969771880164,
				x2: 44.36507936507936,
				y2: 38.773967953069565,
				title: 'Headline1 Input',
				style: {
					backgroundColor: '#fff'
				},
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
			{
				id: 'headline2',
				type: 'text',
				dataId: 'headline2',
				x1: 27.3015873015873,
				y1: 44.67613643641208,
				x2: 44.32539682539682,
				y2: 49.84053385933677,
				title: 'Headline2 Input',
				style: {
					backgroundColor: '#fff'
				},
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
			{
				id: 'headline3',
				type: 'text',
				dataId: 'headline3',
				x1: 27.3015873015873,
				y1: 55.824676904947935,
				x2: 44.36507936507936,
				y2: 61.39894713921586,
				title: 'Headline3 Input',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
				style: {
					backgroundColor: '#fff'
				}
			},
			{
				id: 'viewDisplay',
				type: 'view',
				dataId: 'viewDisplay',
				x1: 56.46825396825397,
				y1: 34.675239839637264,
				x2: 77.46031746031747,
				y2: 91.81150974088354,
				title: 'View Display',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
		],
	},
	{
		id: '010_3',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/010_3.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 43.01587301587302,
				y1: 26.59873977745006,
				x2: 45.43650793650794,
				y2: 30.602851141797384,
				title: "Next",
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				action: {
					type: 'nextScreen',
					screenId: '011'
				},
			},
			{
				x1: 29.246031746031747,
				y1: 34.32095455154847,
				x2: 45.63492063492063,
				y2: 40.184117620771325,
				title: "Desc1",
				action: {
					type: 'inputText',
					inputType: 'input',
					dataContextLabel: 'Description 1',
					dataContextId: 'desc1',
					saveToSelections: true,
				},
			},
			{
				x1: 29.246031746031747,
				y1: 43.11569915538276,
				x2: 45.55555555555556,
				y2: 49.05036421325468,
				title: "Desc2",
				action: {
					type: 'inputText',
					inputType: 'input',
					dataContextLabel: 'Description 2',
					dataContextId: 'desc2',
					saveToSelections: true,
				},
			},
			{
				x1: 0.9126984126984128,
				y1: 8.508736649238056,
				x2: 8.531746031746032,
				y2: 12.2983420476382,
				"title": "Bidding",
				action: {
					type: 'nextScreen',
					screenId: '005_1'
				},
			},
			{
				x1: 0.873015873015873,
				y1: 14.085891763864684,
				x2: 12.023809523809524,
				y2: 18.37601108280824,
				"title": "Campaign Settings",
				action: {
					type: 'nextScreen',
					screenId: '008'
				},
			}
		],
		placeholders: [
			{
				id: 'desc1',
				type: 'text',
				dataId: 'desc1',
				x1: 29.841269841269842,
				y1: 35.107476426688116,
				x2: 45.03968253968254,
				y2: 39.54059972292979,
				title: 'Desc 1 Input',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
				style: {
					backgroundColor: '#fff',
				},
			},
			{
				id: 'desc2',
				type: 'text',
				dataId: 'desc2',
				x1: 29.72222222222222,
				y1: 44.11672699646959,
				x2: 45.07936507936508,
				y2: 48.263842338115026,
				title: 'Desc 2 Input',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
				style: {
					backgroundColor: '#fff',
				},
			},
			{
				id: 'viewDisplay',
				type: 'view',
				dataId: 'viewDisplay',
				x1: 55.87301587301587,
				y1: 20.735576708227196,
				x2: 74.44444444444444,
				y2: 64.8523037046968,
				title: 'View Display',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
		],
	},
	{
		id: '011',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/011.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 88.61111111111111,
				y1: 55.90881823635273,
				x2: 94.24603174603175,
				y2: 63.82723455308938,
				title: "Next",
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				action: {
					type: 'nextScreen',
					screenId: '012_1'
				},
			},
			{
				x1: 23.931962025316455,
				y1: 31.692641178009495,
				x2: 36.34920634920635,
				y2: 42.11157768446311,
				title: "Budget Input",
				action: {
					type: 'inputText',
					inputType: 'input',
					dataContextLabel: 'Budget',
					dataContextId: 'budgetInput',
					saveToSelections: true,
				},
			},
			{
				x1: 0.9126984126984128,
				y1: 15.116976604679063,
				x2: 8.84920634920635,
				y2: 21.715656868626272,
				"title": "Bidding",
				action: {
					type: 'nextScreen',
					screenId: '005_1'
				},
			},
			{
				x1: 0.9126984126984128,
				y1: 25.0749850029994,
				x2: 12.5,
				y2: 31.91361727654469,
				"title": "Campaign Settings",
				action: {
					type: 'nextScreen',
					screenId: '008'
				},
			},
			{
				x1: 0.8333333333333334,
				y1: 47.990401919616076,
				x2: 12.658730158730158,
				y2: 54.82903419316136,
				"title": "Keywords and ads",
				action: {
					type: 'nextScreen',
					screenId: '010_1'
				},
			}
		],
		placeholders: [
			{
				id: 'budgetInput',
				type: 'number',
				dataId: 'budgetInput',
				x1: 26.031746031746035,
				y1: 32.87342531493701,
				x2: 35.91269841269841,
				y2: 41.03179364127175,
				title: 'Buget Input',
				style: {
					fontSize: '1rem'
				},
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
		],
	},
	{
		id: '012_1',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/012_1.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 73.4920634920635,
				y1: 8.13494108351369,
				x2: 83.88888888888889,
				y2: 14.620907623071902,
				title: "Publish",
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				action: {
					type: 'nextScreen',
					screenId: '013'
				},
			},
			{
				x1: 19.444444444444446,
				y1: 15.830155621972587,
				x2: 83.88888888888889,
				y2: 93.55182246040744,
				title: "Continue",
				action: {
					type: 'nextScreen',
					screenId: '012_2'
				},
			},
			{
				x1: 0.992063492063492,
				y1: 11.87261671647944,
				x2: 8.73015873015873,
				y2: 17.69899343845546,
				"title": "Bidding",
				action: {
					type: 'nextScreen',
					screenId: '005_1'
				},
			},
			{
				x1: 0.992063492063492,
				y1: 20.00755779999313,
				x2: 11.230158730158731,
				y2: 25.833934521969148,
				"title": "Campaign Settings",
				action: {
					type: 'nextScreen',
					screenId: '008'
				},
			},
			{
				x1: 0.8333333333333334,
				y1: 38.47607269229448,
				x2: 11.150793650793652,
				y2: 44.96203923185269,
				"title": "Keywords and ads",
				action: {
					type: 'nextScreen',
					screenId: '010_1'
				},
			},
			{
				x1: 0.6349206349206349,
				y1: 47.16067195712666,
				x2: 8.174603174603174,
				y2: 53.31684358789378,
				"title": "Budget",
				action: {
					type: 'nextScreen',
					screenId: '011'
				},
			}
		],
		placeholders: [
			{
				id: 'text',
				type: 'text',
				dataId: 'campainName',
				x1: 34.84126984126984,
				y1: 33.85894396921914,
				x2: 58.92857142857143,
				y2: 40.125047236249955,
				title: 'Campaign Name',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
		],
	},
	{
		id: '012_2',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/012_2.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 33.80952380952381,
				y1: 55.71626708936002,
				x2: 83.17460317460318,
				y2: 60.70933227659996,
				title: "Keyword"
			},
			{
				x1: 33.88888888888889,
				y1: 62.45294234198534,
				x2: 83.13492063492063,
				y2: 67.28749752328116,
				title: "Ads"
			},
			{
				x1: 73.37301587301587,
				y1: 87.49752328115711,
				x2: 83.80952380952381,
				y2: 92.09431345353676,
				title: "Publish",
				backgroundColor: 'rgba(22, 217, 97, 0.5)',
				border: 'rgba(22, 217, 97, 0.8)',
				action: {
					type: 'nextScreen',
					screenId: '013'
				},
			},
			{
				x1: 0.992063492063492,
				y1: 8.718050326926887,
				x2: 7.420634920634921,
				y2: 12.76005547850208,
				"title": "Bidding",
				action: {
					type: 'nextScreen',
					screenId: '005_1'
				},
			},
			{
				x1: 0.9126984126984128,
				y1: 14.662175549831582,
				x2: 11.230158730158731,
				y2: 18.8626907073509,
				"title": "Campaign Settings",
				action: {
					type: 'nextScreen',
					screenId: '008'
				},
			},
			{
				x1: 0.992063492063492,
				y1: 28.056271052110166,
				x2: 11.30952380952381,
				y2: 32.415296215573605,
				"title": "Keywords and ads",
				action: {
					type: 'nextScreen',
					screenId: '010_1'
				},
			},
			{
				x1: 0.9126984126984128,
				y1: 34.07965127798692,
				x2: 8.095238095238095,
				y2: 38.121656429562115,
				"title": "Budget",
				action: {
					type: 'nextScreen',
					screenId: '011'
				},
			}
		],
		placeholders: [
			{
				id: 'budgetInput',
				type: 'number',
				dataId: 'budgetInput',
				x1: 34.007936507936506,
				y1: 78.77947295423023,
				x2: 83.33333333333334,
				y2: 83.93104814741432,
				style: {
					backgroundColor: '#fff',
					color: '#7f7f7f',
					fontSize: '1rem'
				},
				title: 'Buget Input',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
			{
				id: 'location',
				type: 'text',
				dataId: 'audience_location',
				x1: 34.285714285714285,
				y1: 20.051515751931838,
				x2: 83.2936507936508,
				y2: 24.965325936199722,
				style: {
					backgroundColor: '#fff',
					fontWeight: 'bold',
					color: '#7f7f7f'
				},
				title: 'Location Input',
				responsiveConfig: {
					baseFontSize: 1,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
			{
				id: 'language',
				type: 'text',
				dataDisplayType: 'tag',
				dataId: 'audience_language',
				x1: 34.24603174603175,
				y1: 26.31266098672479,
				x2: 83.2936507936508,
				y2: 31.22647117099267,
				style: {
					backgroundColor: '#fff',
				},
				title: 'Location Input',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
			{
				id: "value_network",
				type: 'text',
				x1: 33.88888888888889,
				y1: 13.948880523083021,
				x2: 82.53968253968253,
				y2: 18.704180701406774,
				title: 'Location Input',
				dataId: 'value_network',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
				style: {
					fontWeight: 'bold',
					fontSize: '1rem',
					backgroundColor: '#fff',
					color: '#7f7f7f'
				},
			},
		],
	},
	{
		id: '013',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/013.png',
		bgColor: 'bg-[#64767e]',
		elements: [
			{
				x1: 5.833333333333333,
				y1: 20.232970988059627,
				x2: 16.944444444444446,
				y2: 24.04610782811702,
				title: "Insights",
				action: {
					type: 'nextScreen',
					screenId: '014'
				},
			},
			{
				x1: 77.46031746031747,
				y1: 1.9454779796211181,
				x2: 86.74603174603175,
				y2: 7.0037207266360255,
				title: "Date"
			}
		],
		placeholders: [
			{
				id: 'text',
				type: 'text',
				dataId: 'campainName',
				x1: 27.6984126984127,
				y1: 3.9687750784270808,
				x2: 33.88888888888889,
				y2: 5.836433938863355,
				style: {
					fontSize: '0.75rem',
					backgroundColor: '#fff'
				},
				title: 'Campaign Name',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
			{
				id: 'text',
				type: 'text',
				dataId: 'listKeyword',
				x1: 33.84920634920635,
				y1: 57.81960555433964,
				x2: 59.64285714285714,
				y2: 78.90858685343255,
				style: {
					backgroundColor: '#fff'
				},
				title: 'List Keyword',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
			{
				id: 'text',
				type: 'overviewAds',
				dataId: 'listAds',
				x1: 60.55555555555555,
				y1: 49.88205539748547,
				x2: 86.42857142857143,
				y2: 85.67885022251404,
				style: {
					backgroundColor: '#fff'
				},
				title: 'List Ads',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
		],
		charts: [
			{
				id: 'chart',
				type: 'bar',
				chartArea: {
					x1: 33.80952380952381,
					y1: 9.104836944626832,
					x2: 86.54761904761905,
					y2: 43.423068505143355,
				},
			},
		]
	},
	{
		id: '014',
		title: '',
		image: '/assets/job-simulation/google-ads/simple/014.png',
		bgColor: 'bg-[#64767e]',
		elements: [],
		placeholders: [
			{
				id: 'text',
				type: 'text',
				dataId: 'campainName',
				x1: 39.36507936507937,
				y1: 6.141581161880859,
				x2: 48.45238095238095,
				y2: 8.739942422676608,
				style: {
					backgroundColor: '#fff'
				},
				title: 'Campaign Name',
				responsiveConfig: {
					baseFontSize: 1,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
			{
				id: 'budgetInput',
				type: 'number',
				dataId: 'budgetInput',
				x1: 55.595238095238095,
				y1: 11.65325656356881,
				x2: 62.857142857142854,
				y2: 14.487832484436899,
				style: {
					backgroundColor: '#f1f3f4',
					fontSize: '1rem',
				},
				title: 'Buget Input',
				responsiveConfig: {
					baseFontSize: 0.75,
					minFontSize: 0.3,
					maxFontSize: 1.2,
					scaleWithImage: true,
				},
			},
		],
		charts: [
			{
				id: 'chart',
				type: 'bar',
				chartArea: {
					x1: 29.761904761904763,
					y1: 30.786644029428412,
					x2: 95.5952380952381,
					y2: 90.70642946777885,
				},
			},
		]
	},
];