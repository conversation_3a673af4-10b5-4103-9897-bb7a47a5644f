import React, { useEffect, useState } from 'react';
import { ArrowLeft, Info } from 'lucide-react';
import { Avatar, Button, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, Textarea } from '~/components/ui';
// import TargetingSection from './components/TargetingSection';
import BudgetScheduleSection from './components/BudgetScheduleSection';
import AdPreviewSection from './components/AdPreviewSection';
import GoalSelectionModal from './components/GoalSelectionModal';
import { useToastContext } from '~/Providers';

const AdDetail = ({ onBackToAdList, adId }) => {
	type AdData = {
		id: string;
		goal?: string;
		selectedImages: { preview: string }[];
		postText?: string;
		selectedAudience?: string;
		logo?: string; // Added logo property
	};
	const { showToast } = useToastContext();

	const [isSaving, setIsSaving] = useState(false);
	const [adData, setAdData] = useState<AdData | null>(null);
	const [isGoalModalOpen, setIsGoalModalOpen] = useState(false);
	const [currentGoal, setCurrentGoal] = useState('page-likes');
	const [budget, setBudget] = useState<number>(0);
	const [postText, setPostText] = useState(adData?.postText || '');
	const [audienceInfo, setAudienceInfo] = useState({
		selectedOption: 'advantage',
		gender: 'all',
		ageRange: [18, 65],
		locations: ['Vietnam'],
	});

	const handleGoalChange = (newGoal: string) => {
		setCurrentGoal(newGoal);
	};

	const [buttonLabel, setButtonLabel] = useState('Like Page');

	const labelOptions = [
		'Like Page',
	];

	const handleSaveChanges = async () => {
		if (!adData) return;
		setIsSaving(true);
		const updatedAd = {
			...adData,
			postText,
			budget,
			audienceInfo,
		};

		const storedAds = JSON.parse(localStorage.getItem('ads') || '[]');
		const updatedAds = storedAds.map((item) =>
			item.id === adId ? updatedAd : item
		);

		localStorage.setItem('ads', JSON.stringify(updatedAds));

		await new Promise((resolve) => setTimeout(resolve, 2000));
		setIsSaving(false);
		showToast({ message: 'Ad updated successfully!' });
		onBackToAdList();
	};

	useEffect(() => {
		const storedAds = JSON.parse(localStorage.getItem('ads') || '[]');
		const matchedAd = storedAds.find((item) => item.id === adId);
		setAdData(matchedAd);
		if (matchedAd?.goal) {
			setCurrentGoal(matchedAd.goal);
		}
		if (matchedAd?.budget) {
			setBudget(matchedAd.budget); // set initial budget from adData
		}
		if (matchedAd?.postText) {
			setPostText(matchedAd.postText);
		}
		if (matchedAd?.audienceInfo) {
			setAudienceInfo(matchedAd.audienceInfo);
		}
	}, [adId]);

	return (
		<div className="space-y-4">
			<div>
				<div
					className="flex items-center space-x-1 cursor-pointer"
					onClick={onBackToAdList}
				>
					<ArrowLeft className="w-6 h-6" />
					<span className='text-lg font-medium'>Edit ad</span>
				</div>
			</div>
			<div>
				<div className="grid grid-cols-12 gap-6">
					{/* Left Column - Main Content */}
					<div className="col-span-8 space-y-6">
						<div className='p-4 bg-white drop-shadow-lg rounded'>
							<div className='mb-4 space-y-2'>
								<p className='text-base font-medium'>Description</p>
								<Textarea value={postText} onChange={(e) => setPostText(e.target.value)} />
								<div className='flex justify-center'>
									<img src={adData?.selectedImages[0].preview} className='w-sm' />
								</div>
							</div>
							<div>
								<p className="text-base font-medium mb-2">
									Button label
								</p>
								<Select value={buttonLabel} onValueChange={setButtonLabel}>
									<SelectTrigger className="w-full h-12 text-base border bg-white">
										<SelectValue />
									</SelectTrigger>
									<SelectContent className="bg-white border border-gray-200 shadow-lg z-50">
										{labelOptions.map((option) => (
											<SelectItem key={option} value={option} className="text-base">
												{option}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
						</div>
						<BudgetScheduleSection
							adData={adData}
							budget={budget}
							setBudget={setBudget}
							audienceInfo={audienceInfo}
							setAudienceInfo={setAudienceInfo}
						/>
						{/* <TargetingSection /> */}
					</div>

					{/* Right Column - Preview */}
					<div className="col-span-4">
						<AdPreviewSection budget={budget} postText={postText} image={adData?.selectedImages[0]?.preview} companyName={adData?.selectedAudience} logo={adData?.logo} />
					</div>
				</div>
			</div>
			<div className='text-right mt-6'>
				<Button className='bg-blue-500 hover:bg-blue-600' onClick={handleSaveChanges} disabled={isSaving}>
					{isSaving ? (
						<span className="flex items-center space-x-2">
							<svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
								<circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
								<path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4l5-5-5-5v4a12 12 0 100 24v-4l-5 5 5 5v-4a8 8 0 01-8-8z" />
							</svg>
							<span>Saving...</span>
						</span>
					) : (
						'Save changes'
					)}
				</Button>
			</div>
			<GoalSelectionModal
				isOpen={isGoalModalOpen}
				onClose={() => setIsGoalModalOpen(false)}
				onSave={handleGoalChange}
				currentGoal={currentGoal}
			/>
		</div >
	);
};

export default AdDetail;

{/* <div className="py-6 bg-white drop-shadow-xl flex items-center justify-center gap-10">
				<p className='text-xs font-normal'>By clicking Publish, you agree to Meta's <span className='text-blue-400 cursor-pointer'>Terms & conditions</span>, <span className='text-blue-400 cursor-pointer'>Help Center</span></p>
				<div className='text-xs font-normal text-[#929294]'>
					<p>This ad will save as a draft automatically if you exit before</p>
					<p className='text-end'>submitting.</p>
				</div>
				<div className="flex items-center space-x-2">
					<Button variant="outline" className='rounded'>Cancel</Button>
					<Button className='bg-blue-500 hover:bg-blue-600 rounded'>Publish</Button>
				</div>
			</div> */}