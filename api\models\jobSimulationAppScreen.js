const mongoose = require('mongoose');
const { jobSimulationAppScreenSchema } = require('@librechat/data-schemas');

const JobSimulationAppScreen = mongoose.model(
  'jobSimulationAppScreen',
  jobSimulationAppScreenSchema,
);

/**
 * Create multiple job simulation app screens at once
 * @param {Object[]} screens - Array of screen objects to create
 * @returns {Promise<Object[]>} - Array of created screen objects
 */
const createMany = async function (screens) {
  try {
    const screensArray = Array.isArray(screens) ? screens : [screens];

    if (screensArray.length === 0) {
      return [];
    }

    const { userId, appId } = screensArray[0];

    const maxOrderScreen = await JobSimulationAppScreen.findOne({
      userId,
      appId,
    })
      .sort({ order: -1 })
      .lean();

    let startOrder = 1;
    if (maxOrderScreen) {
      startOrder = maxOrderScreen.order + 1;
    }

    const screensWithOrder = screensArray.map((screen, index) => ({
      ...screen,
      order: screen.order || startOrder + index,
    }));

    const createdScreens = await JobSimulationAppScreen.create(screensWithOrder);

    return createdScreens;
  } catch (error) {
    throw new Error(`Failed to create job simulation app screens: ${error.message}`);
  }
};

/**
 * Update a job simulation app screen by ID
 * @param {string} id - Screen ID to update
 * @param {Object} params - Parameters to update
 * @returns {Promise<Object>} - Updated screen object
 */
const update = async function (id, params) {
  try {
    const { userId, appId } = params;
    const updatedScreen = await JobSimulationAppScreen.findOneAndUpdate(
      {
        _id: id,
        userId,
        appId,
      },
      { $set: params },
      { new: true },
    ).lean();

    return updatedScreen;
  } catch (error) {
    throw new Error(`Failed to update job simulation app screen: ${error.message}`);
  }
};

/**
 * Update the order of screens
 * @param {Object} params - Parameters for updating order
 * @param {string} params.userId - User ID of the screens
 * @param {string} params.appId - App ID of the screens
 * @param {Array<{id: string}>} params.screens - Array of objects with screen ID and new order
 * @returns {Promise<Object[]>} - Array of updated screen objects
 */
const updateOrder = async function (params) {
  // const session = await mongoose.startSession();
  // session.startTransaction();

  try {
    const { userId, appId, screens } = params;

    if (!Array.isArray(screens) || screens.length !== 2) {
      throw new Error('Exactly two screens are required to swap orders');
    }

    const screenIds = screens.map((screen) => screen.id);

    const currentScreens = await JobSimulationAppScreen.find({
      _id: { $in: screenIds },
      userId,
      appId,
    });
    // .session(session);

    if (currentScreens.length !== 2) {
      throw new Error(
        'One or both screens not found or do not belong to the specified user and app',
      );
    }

    const firstScreen = currentScreens[0];
    const secondScreen = currentScreens[1];
    const firstOrder = firstScreen.order;
    const secondOrder = secondScreen.order;

    const updatePromises = [
      JobSimulationAppScreen.findByIdAndUpdate(
        firstScreen._id,
        { $set: { order: secondOrder } },
        { new: true /* session */ },
      ),
      JobSimulationAppScreen.findByIdAndUpdate(
        secondScreen._id,
        { $set: { order: firstOrder } },
        { new: true /* session */ },
      ),
    ];

    const updatedScreens = await Promise.all(updatePromises);
    // await session.commitTransaction();

    return updatedScreens.map((screen) => screen.toObject());
  } catch (error) {
    // await session.abortTransaction();
    throw new Error(`Failed to update screen orders: ${error.message}`);
  } finally {
    // session.endSession();
  }
};

/**
 * Get job simulation app screens by userId and appId with pagination
 * @param {Object} params - Parameters for filtering
 * @param {string} params.userId - User ID to filter by
 * @param {string} params.appId - App ID to filter by
 * @param {number} [params.page=1] - Page number for pagination
 * @param {number} [params.limit=10] - Number of items per page
 * @returns {Promise<Object>} - Object with screens array and pagination info
 */
const getByUserAndApp = async function (params) {
  try {
    const { userId, appId, page = 1, limit = 10 } = params;
    const skip = (page - 1) * limit;

    const total = await JobSimulationAppScreen.countDocuments({
      userId,
      appId,
    });

    const screens = await JobSimulationAppScreen.find({
      userId,
      appId,
    })
      .sort({ order: 1 })
      .skip(skip)
      .limit(limit)
      .lean();

    return {
      screens,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    throw new Error(`Failed to retrieve job simulation app screens: ${error.message}`);
  }
};

/**
 * Get a job simulation app screen by ID
 * @param {string} id - Screen ID to retrieve
 * @returns {Promise<Object>} - Screen object
 */
const getById = async function (id) {
  try {
    const screen = await JobSimulationAppScreen.findById(id).lean();

    if (!screen) {
      throw new Error('Screen not found');
    }

    return screen;
  } catch (error) {
    throw new Error(`Failed to retrieve job simulation app screen: ${error.message}`);
  }
};

/**
 * Delete a job simulation app screen by ID
 * @param {string} id - Screen ID to delete
 * @returns {Promise<Object>} - Deleted screen object
 */
const deleteById = async function (id, params) {
  try {
    const screen = await JobSimulationAppScreen.findOneAndDelete({
      _id: id,
      userId: params.userId,
      appId: params.appId,
    }).lean();

    return screen;
  } catch (error) {
    throw new Error(`Failed to delete job simulation app screen: ${error.message}`);
  }
};

module.exports = {
  model: JobSimulationAppScreen,
  createMany,
  update,
  updateOrder,
  getByUserAndApp,
  getById,
  deleteById,
};
