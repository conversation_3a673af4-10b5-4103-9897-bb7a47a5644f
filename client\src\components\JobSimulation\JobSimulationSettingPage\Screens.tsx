import { useNavigate } from 'react-router-dom';

const screens = [
  {
    id: '001',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/001.png',
  },
  {
    id: '002',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/002.png',
  },
  {
    id: '003',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/003.png',
  },
  {
    id: '004',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/004.png',
  },
];

export default function Screen() {
  const navigate = useNavigate();

  const handleScreenClick = (screen) => {
    navigate(`/job-simulation/setting/${screen.id}`);
  };

  return (
    <>
      <div className="flex flex-wrap gap-4">
        <div className="flex h-40 w-64 items-center justify-center overflow-hidden rounded-xl border border-gray-300 hover:cursor-pointer">
          Upload Screen
        </div>
        {screens.map((screen, index) => (
          <div
            key={index}
            className="flex h-40 w-64 items-center justify-center overflow-hidden rounded-xl border border-gray-300 bg-black hover:cursor-pointer"
            onClick={() => handleScreenClick(screen)}
          >
            <img src={screen.image} alt="screen" className="h-full w-full object-cover" />
          </div>
        ))}
      </div>
    </>
  );
}
