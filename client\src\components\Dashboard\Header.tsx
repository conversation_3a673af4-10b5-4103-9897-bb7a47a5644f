import { useRecoilValue } from "recoil";
import store from "~/store";

const Header = () => {
	const user = useRecoilValue(store.user);
	return <div className="flex justify-between items-center px-10 py-5 bg-white drop-shadow-lg sticky top-0 z-30">
		<p className="text-xl uppercase opacity-50 font-medium">Dashboard</p>
		<div className="flex items-center gap-3">
			<img src={user?.avatar || 'https://images.icon-icons.com/2859/PNG/512/avatar_face_man_boy_profile_smiley_happy_people_icon_181659.png'} className="w-10 h-10 rounded-full border" />
			<div>
				<p className="text-sm font-semibold">{user?.name}</p>
				<p className="text-xs">{user?.role}</p>
			</div>
		</div>
	</div>
}

export default Header;