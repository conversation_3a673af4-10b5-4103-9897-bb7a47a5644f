import { dataService } from 'librechat-data-provider';
import { useEffect, useState } from 'react';
import StatCard from './StatCard';

const stats = [
  {
    title: 'TOTAL JOB SIMULATIONS',
    value: '0',
    unit: 'Jobs',
    dataKey: 'totalJobs',
    dataType: 'number',
  },
  {
    title: 'TOTAL PARTICIPANTS',
    value: '0',
    unit: 'Users',
    dataKey: 'totalParticipants',
    dataType: 'number',
  },
  {
    title: 'TOTAL ATTEMPTS',
    value: '0',
    unit: 'Attempts',
    dataKey: 'totalAttempts',
    dataType: 'number',
  },
  {
    title: 'TOTAL COMPLETIONS',
    value: '0',
    unit: 'Simulations',
    dataKey: 'totalCompletedJobs',
    dataType: 'number',
  },
  {
    title: 'AVG. COMPLETION RATE',
    value: '0',
    unit: '%',
    dataKey: 'avgCompletionRate',
    dataType: 'percentage',
  },
  // {
  //   title: 'REFERENCE LETTERS ISSUED',
  //   value: '0',
  //   unit: 'Letters',
  //   dataKey: 'totalReferenceLetters',
  //   dataType: 'number',
  // },
];

const formatNumber = (value: number, dataType: string) => {
  /**
   * TODO: format value
   * dataType = number
   *  1200 --> 1,200
   * dataType = percentage
   *  x.xx --> x.xx%
   */
  let parsedNumber = Number(value);
  switch (dataType) {
    case 'number':
      return parsedNumber.toLocaleString();
    case 'percentage':
      parsedNumber = Math.round(parsedNumber * 100) / 100;
      return `${parsedNumber}`;
    default:
      return parsedNumber.toLocaleString();
  }
};

const buildStats = (data: any) => {
  return stats.map((stat) => ({
    ...stat,
    value: formatNumber(data?.[stat.dataKey] || 0, stat.dataType || 'number'),
  }));
};

const getOverviewDataAsync = async (params: any): Promise<any> => {
  const result = await dataService.getEmployerDashboardOverview(params);
  // const result = {
  //   data: {
  //     totalJobs: 10500,
  //     totalAttempts: 9345678123,
  //     totalCompletedJobs: 897654999,
  //     avgCompletionRate: 87.34513123,
  //     totalReferenceLetters: 86234987,
  //     totalParticipants: 654999,
  //   },
  // };
  return buildStats(result?.data || {});
};

const OverviewStats = () => {
  const [overviewStats, setOverviewStats] = useState<
    { title: string; value: string; unit: string }[]
  >(buildStats({}));

  useEffect(() => {
    getOverviewDataAsync({}).then((data) => {
      setOverviewStats(data);
    });
  }, []);

  return (
    <div className="xxl:grid-cols-6 grid grid-cols-1 gap-10 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
      {overviewStats.map((stat, index) => (
        <StatCard key={index} {...stat} />
      ))}
    </div>
  );
};

export default OverviewStats;
