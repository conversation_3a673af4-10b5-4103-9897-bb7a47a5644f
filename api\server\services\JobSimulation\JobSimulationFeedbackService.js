const { jobSimulationFeedback, getUserById, JobSimulationProgress } = require('~/models');

const JobSimulationFeedbackService = {
  async create(params) {
    try {
      if (!params || !params.userId) {
        throw new Error('User ID is required to create job simulation feedback');
      }
      if (!params.jobSimulationId) {
        throw new Error('Job simulation ID is required to create job simulation feedback');
      }
      const user = await getUserById(params.userId);
      if (!user) {
        throw new Error('User not found');
      }
      const jobSimulationProgress = await JobSimulationProgress.getProgress({
        jobSimulationId: params.jobSimulationId,
        email: user.email,
      });
      if (!jobSimulationProgress) {
        throw new Error('Job simulation not found');
      }
      if (jobSimulationProgress.status !== 'completed') {
        throw new Error('Job simulation must be completed before providing feedback');
      }

      const feedback = {
        ...params,
        email: user.email,
      };

      const createdFeedback = await jobSimulationFeedback.create(feedback);
      return createdFeedback;
    } catch (error) {
      throw new Error(
        error.message || `Failed to create job simulation feedback: ${error.message}`,
      );
    }
  },

  async update(id, params) {
    try {
      if (!id || !params || !params.userId) {
        throw new Error('ID and user ID are required to update job simulation feedback');
      }
      const user = await getUserById(params.userId);
      if (!user || user._id?.toString() !== params.userId) {
        throw new Error('User not found');
      }
      const feedback = await jobSimulationFeedback.update(id, {
        scores: params.scores,
        content: params.content,
      });
      return feedback;
    } catch (error) {
      throw new Error(
        error.message || `Failed to update job simulation feedback: ${error.message}`,
      );
    }
  },

  async deleteById(id, userId) {
    try {
      if (!id || !userId) {
        throw new Error('ID and user ID are required to delete job simulation feedback');
      }
      const user = await getUserById(userId);
      if (!user || user._id?.toString() !== userId) {
        throw new Error('User not found');
      }

      const feedback = await jobSimulationFeedback.deleteById(id);
      return feedback;
    } catch (error) {
      throw new Error(`Failed to delete job simulation feedback: ${error.message}`);
    }
  },

  async getByUserAndJobSimulation(params) {
    try {
      if (!params || !params.userId || !params.jobSimulationId) {
        throw new Error('User ID and job simulation ID are required to get feedback');
      }
      const feedback = await jobSimulationFeedback.getByUserAndJobSimulation(params);
      return feedback;
    } catch (error) {
      throw new Error(`Failed to get job simulation feedback: ${error.message}`);
    }
  },

  async getById(id) {
    try {
      if (!id) {
        throw new Error('ID is required to get job simulation feedback');
      }
      const feedback = await jobSimulationFeedback.getById(id);
      return feedback;
    } catch (error) {
      throw new Error(`Failed to get job simulation feedback: ${error.message}`);
    }
  },
};

module.exports = JobSimulationFeedbackService;
