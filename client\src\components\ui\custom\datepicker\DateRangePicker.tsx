import { addDays, endOfMonth, endOfWeek, startOfMonth, startOfWeek } from 'date-fns';
import { ChevronDownIcon } from 'lucide-react';
import * as React from 'react';
import { useEffect } from 'react';
import { DateRange } from 'react-day-picker';

import { Button, Calendar, Popover, PopoverContent, PopoverTrigger } from '~/components/ui';

const customRanges = [
  { label: 'Yesterday', value: 'yesterday' },
  { label: 'Today', value: 'today' },
  { label: 'This Week', value: 'week' },
  { label: 'This Month', value: 'month' },
];

export function DateRangePicker({
  onConfirm,
  disabled,
  defaultRange,
}: {
  onConfirm?: (dates: DateRange) => void;
  disabled?: boolean;
  defaultRange?: string;
}) {
  const [open, setOpen] = React.useState(false);
  const [dates, setDates] = React.useState<DateRange | undefined>(undefined);
  const [customRange, setCustomRange] = React.useState<string | undefined>(defaultRange);

  const handleSelectCustomDateRange = (customType: string) => {
    let dateFrom = new Date();
    let dateTo = new Date();

    if (customType === 'yesterday') {
      dateFrom = addDays(new Date(), -1);
      dateTo = addDays(new Date(), -1);
    } else if (customType === 'week') {
      dateFrom = startOfWeek(new Date(), { weekStartsOn: 1 });
      dateTo = endOfWeek(new Date(), { weekStartsOn: 1 });
    } else if (customType === 'month') {
      dateFrom = startOfMonth(new Date());
      dateTo = endOfMonth(new Date());
    }
    setCustomRange(customType);
    setDates({
      from: dateFrom,
      to: dateTo,
    });
  };

  useEffect(() => {
    if (defaultRange) {
      handleSelectCustomDateRange(defaultRange);
    }
  }, []);

  return (
    <div className="flex gap-4">
      <div className="flex flex-col gap-3">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="w-52 justify-between text-center font-normal"
              disabled={!!disabled}
            >
              {dates?.from && dates?.to
                ? `${dates.from.toLocaleDateString()} - ${dates.to.toLocaleDateString()}`
                : 'Select date range'}
              <ChevronDownIcon />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto overflow-hidden p-0" align="start">
            <div className="flex flex-row">
              <div className="flex max-w-36 flex-col space-y-2 border-r border-r-secondary bg-white px-1 py-4">
                {customRanges.map((range) => (
                  <Button
                    key={range.value}
                    variant={customRange === range.value ? 'default' : 'outline'}
                    className="w-full"
                    onClick={() => handleSelectCustomDateRange(range.value)}
                  >
                    {range.label}
                  </Button>
                ))}
              </div>
              <Calendar
                mode="range"
                selected={dates}
                onSelect={(selectedDates) => {
                  setCustomRange(undefined);
                  setDates(selectedDates);
                }}
                disabled={disabled}
                max={31}
                footer={
                  <div className="mt-2 flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => {
                        setCustomRange(undefined);
                        setDates(undefined);
                      }}
                    >
                      Clear
                    </Button>
                    <Button
                      variant="default"
                      className="w-full"
                      onClick={() => {
                        if (dates?.from && dates?.to) {
                          setOpen(false);
                          onConfirm?.(dates);
                        }
                      }}
                    >
                      Confirm
                    </Button>
                  </div>
                }
              />
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
}
