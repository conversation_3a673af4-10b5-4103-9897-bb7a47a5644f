import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer } from "recharts";


const data = [
	{ name: 'ESG Analyst', value: 400, color: '#34A1F4' },
	{ name: 'Digital Marketing', value: 300, color: '#8ACCFF' },
];

const COLORS = ['#34A1F4', '#8ACCFF',];

const RADIAN = Math.PI / 180;
const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }) => {
	const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
	const x = cx + radius * Math.cos(-midAngle * RADIAN);
	const y = cy + radius * Math.sin(-midAngle * RADIAN);

	return (
		<text x={x} y={y} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central">
			{`${(percent * 100).toFixed(0)}%`}
		</text>
	);
};

const UserDistributionPieChart = () => {

	return (
		<div className="bg-white p-6 rounded-xl drop-shadow-lg text-[#505050] flex flex-col">
			<h3 className="text-xl font-semibold mb-4">User Distribution By Job Type</h3>
			<hr />
			<div className="flex flex-col my-4 flex-1">
				<div className="flex items-center justify-center flex-1">
					<div style={{ width: '100%', height: 200 }}>
						<ResponsiveContainer width="100%" height="100%">
							<PieChart>
								<Pie
									data={data}
									cx="50%"
									cy="50%"
									labelLine={false}
									label={renderCustomizedLabel}
									outerRadius={100}
									fill="#8884d8"
									dataKey="value"
								>
									{data.map((entry, index) => (
										<Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
									))}
								</Pie>
							</PieChart>
						</ResponsiveContainer>
					</div>
				</div>
				<div className="flex space-x-8 mt-6">
					{data.map((item) => (
						<div key={item.name} className="flex items-center">
							<div
								className="w-3 h-3 rounded-full mr-2"
								style={{ backgroundColor: item.color }}
							></div>
							<span className="text-sm text-gray-600">{item.name}</span>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};

export default UserDistributionPieChart;