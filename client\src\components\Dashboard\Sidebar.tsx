import { cn } from '~/utils';
import { DashboardIcon, HomeIcon, LogoutJSIcon } from '../svg';
import { useLocation, useNavigate } from 'react-router-dom';

const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const pathSegments = location.pathname.split('/').filter(Boolean);
  const activeTab = pathSegments.includes('overview') ? 'overview' : 'detail';
  const menuItems = [
    {
      id: 'overview',
      label: 'Dashboard',
      icon: HomeIcon,
    },
    {
      id: 'detail',
      label: 'Candidates',
      icon: DashboardIcon,
    },
  ];

  return (
    <div className="flex h-screen w-64 flex-col border-r border-gray-200 bg-white text-[#505050] shadow-sm">
      <div className="flex-1">
        <h2 className="py-4 text-center text-xl font-bold text-[#34A1F4]">JOB SIMULATION</h2>
        <hr />
        <nav className="flex-1 space-y-2 py-6 pl-4">
          {menuItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.id}
                onClick={() => {
                  navigate(
                    item.id === 'overview'
                      ? '/job-simulation/dashboard/overview'
                      : '/job-simulation/dashboard/candidates',
                  );
                }}
                className={cn(
                  'relative flex w-full items-center gap-3 rounded-bl-lg rounded-tl-lg px-3 py-2 text-left transition-colors',
                  activeTab === item.id
                    ? 'font-semibold text-[#34A1F4]'
                    : 'text-gray-600 hover:bg-gray-50',
                )}
              >
                <Icon opacity={activeTab === item.id ? '1' : '0.2'} />
                {item.label}
                {activeTab === item.id && (
                  <div className="absolute bottom-0 right-0 top-0 w-1 rounded-l-sm bg-[#34A1F4]" />
                )}
              </button>
            );
          })}
        </nav>
      </div>

      <div className="w-full space-y-2 border-t py-4 pl-4">
        {/* <button
					onClick={() => onTabChange('settings')}
					className={cn(
						"w-full flex items-center px-3 py-2 text-left transition-colors gap-3 hover:bg-gray-50 rounded-tl-lg rounded-bl-lg relative"
					)}
				>
					<SettingIcon opacity={activeTab === 'settings' ? '1' : '0.2'} />
					Settings
					{activeTab === 'settings' && (
						<div className="absolute right-0 top-0 bottom-0 w-1 bg-[#34A1F4] rounded-l-sm" />
					)}
				</button> */}
        <button
          className={cn(
            'flex w-full items-center gap-3 rounded-bl-lg rounded-tl-lg px-3 py-2 text-left text-[#F2433A] transition-colors hover:bg-gray-50',
          )}
        >
          <LogoutJSIcon />
          Log Out
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
