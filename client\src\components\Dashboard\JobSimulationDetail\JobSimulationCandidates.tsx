import { useEffect, useState } from 'react';
import JobSelectionCard from './JobSelectionCard';
import JobSimulationStats from './JobSimulationStats';
import JobSimulationTalentsTable from './JobSimulationTalentsTable';
import { dataService } from 'librechat-data-provider';
import { useNavigate, useParams } from 'react-router-dom';

const talents = [
  { rank: 1, name: '<PERSON>', task: '2/2', score: 97, time: '2 hours', medal: '🥇' },
  { rank: 2, name: '<PERSON>', task: '2/2', score: 95, time: '2.3 hours', medal: '🥈' },
  { rank: 3, name: '<PERSON>', task: '2/2', score: 93, time: '3 hours', medal: '🥉' },
  { rank: 4, name: '<PERSON>', task: '2/2', score: 91, time: '2.1 hours', medal: '4' },
  { rank: 5, name: '<PERSON>', task: '2/2', score: 90, time: '3 hours', medal: '5' },
  { rank: 6, name: '<PERSON>', task: '2/2', score: 89, time: '3 hours', medal: '6' },
  { rank: 7, name: '<PERSON>', task: '2/2', score: 88, time: '3 hours', medal: '7' },
  { rank: 8, name: '<PERSON> <PERSON>c<PERSON>inney', task: '2/2', score: 82, time: '3 hours', medal: '8' },
  { rank: 9, name: 'Darlene <PERSON>', task: '2/2', score: 72, time: '3 hours', medal: '9' },
  { rank: 10, name: '<PERSON> <PERSON>', task: '1/2', score: 50, time: '3 hours', medal: '10' },
];

interface JobInfoProps {
  jobSimulation: {
    description: string;
    name: string;
    banner?: string;
  };
  overview: {
    totalParticipants: number;
    totalCompletedJobs: number;
    avgFeedbackScores: number;
    totalAttempts: number;
    avgCompletionMins: number;
    avgCompletionRate: number;
    minCompletionMins: number;
  };
}
const JobDetail = () => {
  const navigate = useNavigate();
  const params = useParams();
  const tab = params.tab as 'esg-analyst' | 'digital-marketing';
  const [selectedJob, setSelectedJob] = useState<'esg-analyst' | 'digital-marketing'>(
    tab || 'esg-analyst',
  );
  const [isGettingData, setIsGettingData] = useState(false);
  const [jobOverView, setJobOverview] = useState<JobInfoProps | null>(null);

  const getJobOverview = async (params: any): Promise<any> => {
    const result = await dataService.getJobDetailOverview(params);
    return result?.data || [];
  };

  useEffect(() => {
    if (selectedJob && !isGettingData) {
      getJobOverview({
        jobSimulationId: selectedJob,
      })
        .then((data) => {
          setJobOverview(data);
        })
        .finally(() => {
          setIsGettingData(false);
        });
    }
  }, [selectedJob, isGettingData]);

  return (
    <div className="min-h-screen bg-gray-50 p-12">
      <div className="mx-auto">
        <h1 className="mb-6 text-xl font-medium text-[#505050]">JOB SIMULATION CANDIDATES</h1>

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 rounded-xl bg-white p-6 text-[#505050] drop-shadow-lg">
            <div>
              xxx
            </div>
            <div className="mb-6">
              <h2 className="mb-8 text-2xl font-bold">{jobOverView?.jobSimulation?.name}</h2>
              <div className="relative rounded-lg bg-white bg-opacity-20">
                <h3 className="absolute -top-4 left-4 w-fit rounded-full bg-[#F7F7F7] px-2 py-1 text-sm">
                  Job Description
                </h3>
                <p className="rounded-lg bg-[#F7F7F7] p-4 text-sm leading-relaxed">
                  {jobOverView?.jobSimulation?.description}
                </p>
              </div>
            </div>
            <JobSimulationStats overview={jobOverView?.overview || null} />
            <hr />
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200 text-sm text-[#505050] opacity-60">
                    <th className="px-4 py-3 text-left font-light">#</th>
                    <th className="px-4 py-3 text-left font-light">Candidate Name</th>
                    <th className="px-4 py-3 text-left font-light">Task</th>
                    <th className="px-4 py-3 text-left font-light">Completion Time</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100">
                  {talents.map((talent) => (
                    <tr key={talent.rank} className="hover:bg-gray-50">
                      <td className="px-4 py-4 text-[#505050]">
                        <span className="text-lg">{talent.medal}</span>
                      </td>
                      <td className="px-4 py-4">
                        <div>
                          {/* <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full mr-3"></div> */}
                          <p className="font-medium">{talent.name}</p>
                          <p className="text-xs font-light"><EMAIL></p>
                        </div>
                      </td>
                      <td className="px-4 py-4 text-[#505050]">
                        <span>{talent.task}</span>
                      </td>
                      <td className="px-4 py-4 text-[#505050]">
                        <span>{talent.time}</span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobDetail;
