import { useEffect, useRef, useState } from 'react';
import { Clipboard } from '~/components/svg';
import { Button, Input } from '~/components/ui';
import { useToastContext } from '~/Providers';
import SettingModal from './SettingModal';

const parseStringToArray = (str: string) => {
  return (str || '')
    .split(',')
    .map((item) => item.trim()) // Loại bỏ khoảng trắng dư
    .filter((item) => item !== '');
};

type Coordinate = {
  x: number;
  y: number;
};

type InteractiveElement = {
  id: string;
  title?: string;
  x1: number;
  y1: number;
  x2: number;
  y2: number;
  // top: number;
  // left: number;
  // x: number;
  // y: number;
  // width: number;
  // height: number;
};

const modalConfigDefault = {
  title: 'Audience Settings',
  description: 'Configure your audience targeting',
  inputs: [
    {
      id: 'location',
      type: 'multiSelect',
      label: 'Location',
      dataId: 'audience_location',
      options: ['Vietnam', 'HongKong', 'China', 'Australia', 'Singapore'],
      required: true,
      minSelections: 1,
      defaultValue: ['Singapore'],
    },
    {
      id: 'age',
      type: 'range',
      label: 'Age Range',
      dataId: 'audience_age',
      min: 18,
      max: 65,
      defaultValue: [18, 65],
      labels: { min: '18', max: '65+' },
      formatType: 'age-range',
      formatConfig: {
        maxValue: 50,
        maxLabel: '65+',
        separator: ' - ',
      },
    },
    {
      id: 'gender',
      type: 'radio',
      label: 'Gender',
      dataId: 'audience_gender',
      radioOptions: [
        { value: 'All', label: 'All' },
        { value: 'Men', label: 'Men' },
        { value: 'Women', label: 'Women' },
      ],
      defaultValue: 'All',
    },
  ],
};

export default function TestImageClick({ image }: { image?: string }) {
  const imageRef = useRef<HTMLImageElement>(null);
  const { showToast } = useToastContext();

  const [isAddingElement, setIsAddingElement] = useState(false);
  const [coordinates, setCoordinates] = useState<Coordinate[]>([]);
  const [elements, setElements] = useState<InteractiveElement[]>([]);
  const [placeholders, setPlaceholders] = useState<InteractiveElement[]>([]);
  const [charts, setCharts] = useState<InteractiveElement[]>([]);
  const [jsonInput, setJsonInput] = useState('');
  const [elementName, setElementName] = useState('');
  const [exportJson, setExportJson] = useState('');

  // Selection mode: "drag" or "points"
  const [selectionMode, setSelectionMode] = useState<'drag' | 'points'>('drag');

  // Drag and drop states
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState<Coordinate | null>(null);
  const [dragEnd, setDragEnd] = useState<Coordinate | null>(null);
  const [selectionBox, setSelectionBox] = useState<{
    left: number;
    top: number;
    width: number;
    height: number;
  } | null>(null);

  // Modal state for element naming
  const [isNamingModalOpen, setIsNamingModalOpen] = useState(false);
  const [tempElement, setTempElement] = useState<Omit<InteractiveElement, 'name'> | null>(null);

  // Reset states when exiting adding mode or changing selection mode
  useEffect(() => {
    if (!isAddingElement) {
      setCoordinates([]);
      setDragStart(null);
      setDragEnd(null);
      setSelectionBox(null);
      setIsDragging(false);
    }
  }, [isAddingElement, selectionMode]);

  // Calculate position relative to image as percentage
  const getRelativePosition = (event: React.MouseEvent<HTMLImageElement>): Coordinate | null => {
    const image = imageRef.current;
    if (!image) return null;

    const rect = image.getBoundingClientRect();
    const x = ((event.clientX - rect.left) / rect.width) * 100;
    const y = ((event.clientY - rect.top) / rect.height) * 100;

    return { x, y };
  };
  const handleMouseDown = (event: React.MouseEvent<HTMLImageElement>) => {
    if (!isAddingElement || selectionMode !== 'drag') return;

    // Prevent default behavior to stop image dragging
    event.preventDefault();

    const pos = getRelativePosition(event);
    if (pos) {
      setIsDragging(true);
      setDragStart(pos);
      setDragEnd(pos);
      setSelectionBox({
        left: pos.x,
        top: pos.y,
        width: 0,
        height: 0,
      });
    }
  };

  const handleMouseMove = (event: React.MouseEvent<HTMLImageElement>) => {
    if (!isAddingElement || selectionMode !== 'drag' || !isDragging || !dragStart) return;

    // Prevent default to stop image dragging
    event.preventDefault();

    const pos = getRelativePosition(event);
    if (pos) {
      setDragEnd(pos);

      // Calculate selection box dimensions
      const left = Math.min(dragStart.x, pos.x);
      const top = Math.min(dragStart.y, pos.y);
      const width = Math.abs(dragStart.x - pos.x);
      const height = Math.abs(dragStart.y - pos.y);

      setSelectionBox({ left, top, width, height });
    }
  };

  const handleMouseUp = (event: React.MouseEvent<HTMLImageElement>) => {
    if (!isAddingElement || selectionMode !== 'drag' || !isDragging || !dragStart || !dragEnd)
      return;

    // Prevent default
    event.preventDefault();

    setIsDragging(false);

    // Only create element if drag is significant
    createElementFromDrag();
    // if (Math.abs(dragStart.x - dragEnd.x) > 1 && Math.abs(dragStart.y - dragEnd.y) > 1) {
    //   createElementFromDrag();
    // } else {
    //   // Reset if it was just a click
    //   setDragStart(null);
    //   setDragEnd(null);
    //   setSelectionBox(null);
    // }
  };
  const createElementFromDrag = () => {
    if (!dragStart || !dragEnd || !selectionBox) return;

    const left = selectionBox.left;
    const top = selectionBox.top;
    const width = selectionBox.width;
    const height = selectionBox.height;

    // Create coordinates for compatibility
    const coordinates = [
      { x: left, y: top }, // Top-left
      { x: left + width, y: top }, // Top-right
      { x: left + width, y: top + height }, // Bottom-right
      { x: left, y: top + height }, // Bottom-left
    ];

    // Create a temporary element (without adding it yet)
    const tempElementData: Omit<InteractiveElement, 'name'> = {
      id: `element-${Date.now()}`,
      x1: left,
      y1: top,
      x2: left + width,
      y2: top + height,
      // top: top,
      // left: left,
      // x: left,
      // y: top,
      // width: width,
      // height: height,
    };

    // Store temp element and open naming modal
    setTempElement(tempElementData);
    setElementName(''); // Reset any previous name
    setIsNamingModalOpen(true);

    // Keep the selection and states until element is saved or canceled
  };

  // Function to save element after naming
  const saveElementWithName = (data?: any) => {
    if (!tempElement) return;

    // Create full element with name
    const newElement = {
      ...tempElement,
      title: data.title.trim() || '',
      backgroundColor: data.actionBgColor,
      border: data.actionBorderColor,
      actions: data.actions.map((action: any) => {
        const result: any = {
          type: action.actionType?.value,
          dataContextId: action.contextId,
          screenId: action.screen?.value,
          inputTextType: action.inputType?.value,
          dataContextLabel: action.contextLabel,
          message: action.message,
        };

        if (action.actionType?.value === 'dropdown') {
          result.dropdownOptions = action?.dropdown;
        } else if (action.actionType?.value === 'audience') {
          result.modalConfig = modalConfigDefault;
        }

        return result;
      }),
    };

    // Add to elements
    const elementsUpdated = [...elements];
    if (data.contentType?.value === 'elements') {
      elementsUpdated.push(newElement);
    }
    setElements(elementsUpdated);

    const newPlaceholder = {
      ...tempElement,
      title: data.title.trim() || '',
      // type: data.placeholderType?.value,
      dataContextId: data.contextId,
      style: {
        backgroundColor: data.actionBgColor,
        border: data.actionBorderColor,
        whiteSpace: data.whiteSpace?.value,
        overflow: data.overflow?.value,
        textOverflow: data.textOverflow?.value,
        alignItems: data.alignItems?.value,
        fontWeight: data.fontWeight?.value,
        fontSize: data.fontSize,
      },
      dataByTime: data.dataByTime,
    };
    const placeholdersUpdated = [...placeholders];
    if (data.contentType?.value === 'placeholders') {
      placeholdersUpdated.push(newPlaceholder);
    }
    setPlaceholders(placeholdersUpdated);

    const newChart = {
      ...tempElement,
      title: data.title.trim() || '',
      type: 'line-time-dynamic',
      // dataContextId: data.contextId,
      data: {
        labelX: data.labelX,
        labelY: data.labelY,
        timePoints: parseStringToArray(data.timePoints),
        xLabels: parseStringToArray(data.xLabels),
        lines: data.lines.map((line) => ({
          name: line.name,
          dataContextId: line.contextId,
          type: line.type?.value,
          stroke: line.stroke,
          staticValues: parseStringToArray(line.staticValues),
        })),
      },
    };
    const chartsUpdated = [...charts];
    if (data.contentType?.value === 'charts') {
      chartsUpdated.push(newChart);
    }
    setCharts(chartsUpdated);

    setExportJson(
      JSON.stringify({
        id: tempElement.id,
        title: '',
        image: image,
        bgColor: data.screenBgColor,
        elements: elementsUpdated,
        placeholders: placeholdersUpdated,
        charts: chartsUpdated,
      }),
    );

    // Reset all states
    setDragStart(null);
    setDragEnd(null);
    setSelectionBox(null);
    setElementName('');
    setTempElement(null);
    setIsNamingModalOpen(false);

    // Exit adding mode
    setIsAddingElement(false);
  };
  // Function to cancel element creation
  const cancelElementCreation = () => {
    // Reset all states
    setDragStart(null);
    setDragEnd(null);
    setSelectionBox(null);
    setElementName('');
    setTempElement(null);
    setIsNamingModalOpen(false);

    // Only clear coordinates if in points mode
    if (selectionMode === 'points') {
      setCoordinates([]);
    }
  };
  // Click handler for "points" mode (2-point mode)
  const handleClick = (event: React.MouseEvent<HTMLImageElement>) => {
    if (!isAddingElement || selectionMode !== 'points') return;

    // Prevent default to stop any image dragging
    event.preventDefault();

    const pos = getRelativePosition(event);
    if (!pos) return;

    // If we already have 2 points or modal is open, do nothing
    if (coordinates.length >= 2 || isNamingModalOpen) {
      return;
    }

    // Add coordinate in points mode
    setCoordinates([...coordinates, pos]);
  };

  const handleCopyElement = (id: string) => {
    const element = elements.find((element) => element.id === id);
    if (element) {
      handleCopyToClipboard(JSON.stringify(element));
    }
  };

  const handleDeleteElement = (id: string) => {
    if (confirm('Delete this element?')) {
      const eleFiltered = elements.filter((element) => element.id !== id);
      const exportJSONCurrent = JSON.parse(exportJson || '{}');
      setExportJson(JSON.stringify({ ...exportJSONCurrent, elements: eleFiltered }));
      setElements(eleFiltered);
    }
  };

  // Handle double click on elements to delete them
  const handleElementDoubleClick = (id: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering image click
    handleDeleteElement(id);
  };
  // Reset any selection - no longer needed with drag approach
  const resetCoordinates = () => {
    setSelectionBox(null);
    setDragStart(null);
    setDragEnd(null);
  };

  const handleExport = () => {
    // const json = JSON.stringify(elements, null, 2);
    // setExportJson(json);

    navigator.clipboard.writeText(exportJson);
  };

  const handleCopyToClipboard = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        showToast({
          status: 'success',
          message: 'Copied!',
        });
      })
      .catch((err) => {
        console.error('Failed to copy text: ', text, err);
      });
  };

  const handleImport = () => {
    try {
      const importedElements = JSON.parse(jsonInput);
      if (Array.isArray(importedElements)) {
        setElements(importedElements);
      }
    } catch (error) {
      alert('Invalid JSON format');
    }
  };

  const createElementFromPoints = (payload: any) => {
    const points: Coordinate[] = payload?.points;
    if (points.length !== 2 || !tempElement) return;

    // // Create element with name from the temporary element
    // const newElement: InteractiveElement = {
    //   ...tempElement,
    //   title: elementName.trim() || undefined,
    // };

    // // Add to elements
    // setElements([...elements, newElement]);

    const newElement = {
      ...tempElement,
      title: payload.title.trim() || '',
      backgroundColor: payload.actionBgColor,
      border: payload.actionBorderColor,
      actions: payload.actions.map((action: any) => {
        const result: any = {
          type: action.actionType?.value,
          dataContextId: action.contextId,
          screenId: action.screen?.value,
          inputTextType: action.inputType?.value,
          dataContextLabel: action.contextLabel,
          message: action.message,
        };

        if (action.actionType?.value === 'dropdown') {
          result.dropdownOptions = action?.dropdown;
        } else if (action.actionType?.value === 'audience') {
          result.modalConfig = modalConfigDefault;
        }

        return result;
      }),
    };

    // Add to elements
    const elementsUpdated = [...elements];
    if (payload.contentType?.value === 'elements') {
      elementsUpdated.push(newElement);
    }
    setElements(elementsUpdated);

    const newPlaceholder = {
      ...tempElement,
      title: payload.title.trim() || '',
      // type: payload.placeholderType?.value,
      dataContextId: payload.contextId,
      style: {
        backgroundColor: payload.actionBgColor,
        border: payload.actionBorderColor,
        whiteSpace: payload.whiteSpace?.value,
        overflow: payload.overflow?.value,
        textOverflow: payload.textOverflow?.value,
        alignItems: payload.alignItems?.value,
        fontWeight: payload.fontWeight?.value,
        fontSize: payload.fontSize,
      },
      dataByTime: payload.dataByTime,
    };
    const placeholdersUpdated = [...placeholders];
    if (payload.contentType?.value === 'placeholders') {
      placeholdersUpdated.push(newPlaceholder);
    }
    setPlaceholders(placeholdersUpdated);

    const newChart = {
      ...tempElement,
      title: payload.title.trim() || '',
      type: 'line-time-dynamic',
      // dataContextId: data.contextId,
      data: {
        labelX: payload.labelX,
        labelY: payload.labelY,
        timePoints: parseStringToArray(payload.timePoints),
        xLabels: parseStringToArray(payload.xLabels),
        lines: payload.lines.map((line) => ({
          name: line.name,
          dataContextId: line.contextId,
          type: line.type?.value,
          stroke: line.stroke,
          staticValues: parseStringToArray(line.staticValues),
        })),
      },
    };
    const chartsUpdated = [...charts];
    if (payload.contentType?.value === 'charts') {
      chartsUpdated.push(newChart);
    }
    setCharts(chartsUpdated);

    setExportJson(
      JSON.stringify({
        id: tempElement.id,
        title: '',
        image: image,
        bgColor: payload.screenBgColor,
        elements: elementsUpdated,
        placeholders: placeholdersUpdated,
        charts: chartsUpdated,
      }),
    );

    // Reset states
    setCoordinates([]);
    setElementName('');
    setTempElement(null);
    setSelectionBox(null);
    setIsNamingModalOpen(false);

    // Exit adding mode
    setIsAddingElement(false);
  };

  const onFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!!e.target.files?.length && imageRef?.current) {
      imageRef.current.src = URL.createObjectURL(e.target.files[0]);
      setElements([]);
      setExportJson('');
    }
  };

  useEffect(() => {
    if (image && imageRef.current) {
      imageRef.current.src = image;
      setElements([]);
      setExportJson('');
    }
  }, [image]);

  // Effect to handle when 2 points are selected (top-left and bottom-right)
  useEffect(() => {
    if (isAddingElement && selectionMode === 'points' && coordinates.length === 2) {
      // First point is top-left, second point is bottom-right
      const topLeft = coordinates[0];
      const bottomRight = coordinates[1];

      // Ensure proper ordering (in case user clicked in different order)
      const x1 = Math.min(topLeft.x, bottomRight.x);
      const y1 = Math.min(topLeft.y, bottomRight.y);
      const x2 = Math.max(topLeft.x, bottomRight.x);
      const y2 = Math.max(topLeft.y, bottomRight.y);

      // Create a temporary selection box to visualize the area
      setSelectionBox({
        left: x1,
        top: y1,
        width: x2 - x1,
        height: y2 - y1,
      });

      // Create temporary element data
      const tempElementData: Omit<InteractiveElement, 'name'> = {
        id: `element-${Date.now()}`,
        x1: x1,
        y1: y1,
        x2: x2,
        y2: y2,
      };

      // Store temp element and open naming modal
      setTempElement(tempElementData);
      setElementName(''); // Reset any previous name
      setIsNamingModalOpen(true);
    }
  }, [coordinates.length, isAddingElement, selectionMode]);
  // Effect to handle keyboard shortcuts for modal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isNamingModalOpen) return;

      if (e.key === 'Escape') {
        cancelElementCreation();
      } else if (e.key === 'Enter') {
        if (selectionMode === 'points') {
          createElementFromPoints({ points: coordinates });
        } else {
          saveElementWithName();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isNamingModalOpen, selectionMode, coordinates]);

  return (
    <div>
      {/* <Input type="file" onChange={onFileChange} /> */}
      <div className="flex w-full flex-col gap-4">
        <div className="relative w-full">
          {elements.map((element) => (
            <div
              key={element.id}
              className="group absolute cursor-pointer border-2 border-blue-500 bg-blue-200 bg-opacity-30 transition-all hover:bg-opacity-50"
              style={{
                left: `${element.x1}%`,
                top: `${element.y1}%`,
                width: `${element.x2 - element.x1}%`,
                height: `${element.y2 - element.y1}%`,
              }}
              onClick={() => {
                // Copy id to clipboard
                navigator.clipboard.writeText(element.id);
              }}
              title="Double-click to delete"
            >
              <div
                className="absolute right-0 top-0 hidden rounded-bl bg-red-500 px-1.5 py-0.5 text-xs text-white group-hover:block"
                onClick={(e) => handleElementDoubleClick(element.id, e)}
              >
                x
              </div>
              {/* <div className="flex absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 justify-center">
                {element.name ? `${element.name} (${element.id})` : element.id}
              </div> */}
            </div>
          ))}{' '}
          {isAddingElement && selectionBox && (
            <div
              className={`absolute border-2 ${selectionMode === 'drag' ? 'border-green-500 bg-green-200' : 'border-purple-500 bg-purple-200'} pointer-events-none bg-opacity-30`}
              style={{
                left: `${selectionBox.left}%`,
                top: `${selectionBox.top}%`,
                width: `${selectionBox.width}%`,
                height: `${selectionBox.height}%`,
              }}
            />
          )}
          {isAddingElement &&
            selectionMode === 'points' &&
            coordinates.map((coord, index) => (
              <div
                key={index}
                className="pointer-events-none absolute h-3 w-3 -translate-x-1/2 -translate-y-1/2 transform rounded-full bg-red-500"
                style={{
                  left: `${coord.x}%`,
                  top: `${coord.y}%`,
                  zIndex: 20,
                }}
              >
                <div className="absolute left-4 top-4 rounded bg-white px-1 text-xs shadow">
                  {index === 0 ? 'TL' : 'BR'}
                </div>
              </div>
            ))}{' '}
          <img
            ref={imageRef}
            onClick={handleClick}
            // src={`/assets/job-simulation/xad/${imageId}`}
            alt="Test"
            className={`w-full select-none ${selectionMode === 'drag' ? 'cursor-crosshair' : 'cursor-pointer'}`}
            onDoubleClick={(e) => {
              e.preventDefault();
              if (!isAddingElement) setIsAddingElement(true);
            }}
            onMouseDown={(e) => {
              // Prevent default to stop image drag
              e.preventDefault();
              handleMouseDown(e);
            }}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            // Also handle mouse leave to prevent issues when dragging out of the image
            onMouseLeave={(e) => {
              if (isDragging && selectionMode === 'drag') {
                handleMouseUp(e);
              }
            }}
          />
        </div>
        <hr className="my-2" />
        <div className="mb-2 flex gap-2">
          <Button
            size="sm"
            onClick={() => setIsAddingElement(!isAddingElement)}
            variant={isAddingElement ? 'secondary' : 'default'}
          >
            {isAddingElement ? 'Cancel' : 'Add New Element'}
          </Button>{' '}
          {isAddingElement && (
            <>
              <div className="flex gap-1">
                <Button
                  size="sm"
                  variant={selectionMode === 'drag' ? 'default' : 'outline'}
                  onClick={() => setSelectionMode('drag')}
                >
                  Drag Mode
                </Button>
                <Button
                  size="sm"
                  variant={selectionMode === 'points' ? 'default' : 'outline'}
                  onClick={() => setSelectionMode('points')}
                >
                  2-Point Mode
                </Button>
                {selectionMode === 'points' && coordinates.length > 0 && (
                  <Button size="sm" variant="secondary" onClick={resetCoordinates}>
                    Clear Points
                  </Button>
                )}
              </div>
            </>
          )}
          <Button size="sm" onClick={handleExport} variant="outline">
            Export JSON
          </Button>
          {elements.length > 0 && (
            <div className="ml-auto flex items-center gap-2">
              <span className="text-sm text-gray-600">
                {elements.length} element{elements.length !== 1 ? 's' : ''}
              </span>
              <Button
                onClick={() => {
                  if (confirm('Delete all elements?')) {
                    setElements([]);
                    setExportJson('');
                  }
                }}
                variant="destructive"
                size="sm"
              >
                Clear All
              </Button>
            </div>
          )}
        </div>
        {isAddingElement && (
          <>
            <div className="flex-1">
              <Input
                placeholder="Element Name (optional)"
                value={elementName}
                onChange={(e) => setElementName(e.target.value)}
              />
            </div>{' '}
            <div className="mb-4 rounded-md border border-[#424242] p-3">
              <h3 className="mb-2 font-medium">
                Selection mode:{' '}
                {selectionMode === 'drag'
                  ? 'Drag to create element'
                  : 'Click 2 points to create element'}
              </h3>

              {selectionMode === 'drag' && (
                <div className="select-none rounded border bg-white p-2 text-sm text-gray-600">
                  Click and drag on the image to create an element. Release the mouse button when
                  you're satisfied with the selection.
                  {selectionBox && (
                    <div className="mt-2 rounded border border-blue-200 bg-blue-50 p-2">
                      <div className="font-medium">Current Selection:</div>
                      <div>
                        Position: left {selectionBox.left.toFixed(2)}%, top{' '}
                        {selectionBox.top.toFixed(2)}%
                      </div>
                      <div>
                        Size: {selectionBox.width.toFixed(2)}% × {selectionBox.height.toFixed(2)}%
                      </div>
                    </div>
                  )}
                </div>
              )}

              {selectionMode === 'points' && (
                <div className="select-none rounded border bg-white p-2 text-sm text-gray-600">
                  Click 2 points on the image: first the top-left corner, then the bottom-right
                  corner.
                  <div className="mt-2 grid grid-cols-2 gap-2">
                    {[0, 1].map((index) => (
                      <div key={index} className="rounded border bg-white p-2 text-center">
                        {coordinates[index] ? (
                          <div>
                            <div>{index === 0 ? 'Top-Left' : 'Bottom-Right'}</div>
                            <div className="text-sm text-gray-600">
                              {coordinates[index].x.toFixed(2)}% × {coordinates[index].y.toFixed(2)}
                              %
                            </div>
                          </div>
                        ) : (
                          <div className="text-gray-400">
                            {index === 0 ? 'Top-Left' : 'Bottom-Right'}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>{' '}
                  {coordinates.length === 2 && tempElement && (
                    <Button
                      onClick={() => setIsNamingModalOpen(true)}
                      className="mt-3 w-full"
                      variant="default"
                      size="sm"
                    >
                      Name & Create Element
                    </Button>
                  )}
                </div>
              )}
            </div>
          </>
        )}
        <div className="mt-4 grid grid-cols-1 gap-4">
          {elements.length > 0 && (
            <div>
              <h3 className="mb-2 font-medium">Element List</h3>
              <div className="overflow-hidden rounded-md border">
                {' '}
                <table className="w-full text-sm">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="p-2 text-left">ID</th>
                      <th className="p-2 text-left">Name</th>
                      <th className="p-2 text-left">Coordinates</th>
                      <th className="p-2 text-left">Position</th>
                      <th className="p-2 text-left">Size</th>
                      <th className="p-2 text-right">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {elements.map((element) => (
                      <tr key={element.id} className="border-t">
                        <td className="p-2">{element.id.split('-')[1]}</td>
                        <td className="p-2">{element.title || '-'}</td>
                        <td className="p-2">
                          ({element.x1.toFixed(1)},{element.y1.toFixed(1)}) - (
                          {element.x2.toFixed(1)},{element.y2.toFixed(1)})
                        </td>
                        <td className="p-2">
                          left: {element.x1.toFixed(1)}%, top: {element.y1.toFixed(1)}%
                        </td>
                        <td className="p-2">
                          {(element.x2 - element.x1).toFixed(2)}% x{' '}
                          {(element.y2 - element.y1).toFixed(2)}%
                        </td>
                        <td className="p-2 text-right">
                          <div className="flex justify-end gap-2">
                            <button
                              className="text-green-500 hover:text-green-700"
                              onClick={() => handleCopyElement(element.id)}
                            >
                              Copy
                            </button>
                            <button
                              className="text-red-500 hover:text-red-700"
                              onClick={() => handleDeleteElement(element.id)}
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* <div>
            <h3 className="mb-2 font-medium">Import Elements</h3>
            <div className="flex gap-2">
              <Input
                className="flex-1 border border-[#424242]"
                placeholder="Paste JSON array here"
                value={jsonInput}
                onChange={(e) => setJsonInput(e.target.value)}
              />
              <Button onClick={handleImport}>Import</Button>
            </div>
          </div> */}

          {exportJson && (
            <div>
              <div className="mb-2 flex gap-2">
                <h3 className="font-medium">Exported Elements</h3>
                <div className="cursor-pointer" onClick={() => handleCopyToClipboard(exportJson)}>
                  <Clipboard />
                </div>
              </div>
              <pre className="max-h-[70vh] overflow-auto rounded-md border border-[#424242] p-3">
                {exportJson}
              </pre>
            </div>
          )}
        </div>
      </div>
      {/* Modal for element naming */}
      {isNamingModalOpen && (
        // <div className="animate-fadeIn fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        //   <div className="animate-scaleIn w-96 max-w-full rounded-lg bg-white p-6 shadow-lg">
        //     <h3 className="mb-4 text-lg font-medium">Name this element</h3>
        //     <p className="mb-4 text-sm text-gray-600">
        //       Give this element a descriptive name (optional)
        //     </p>{' '}
        //     <Input
        //       className="mb-4 w-full"
        //       placeholder="Enter element name"
        //       value={elementName}
        //       onChange={(e) => setElementName(e.target.value)}
        //       autoFocus
        //     />
        //     <div className="flex justify-end gap-2">
        //       <Button variant="outline" onClick={cancelElementCreation}>
        //         Cancel
        //       </Button>{' '}
        //       <Button
        //         onClick={
        //           selectionMode === 'points'
        //             ? () => createElementFromPoints(coordinates)
        //             : saveElementWithName
        //         }
        //       >
        //         Save
        //       </Button>
        //     </div>
        //   </div>
        // </div>
        <SettingModal
          open={isNamingModalOpen}
          onClose={() => setIsNamingModalOpen(false)}
          onOk={(data) => {
            selectionMode === 'points'
              ? createElementFromPoints({ ...data, points: coordinates })
              : saveElementWithName(data);
          }}
        />
      )}
    </div>
  );
}
