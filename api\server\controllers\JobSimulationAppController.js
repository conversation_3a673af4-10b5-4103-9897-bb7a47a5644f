const JobSimulationAppService = require('~/server/services/JobSimulation/JobSimulationAppService.js');

const JobSimulationAppController = {
  async create(req, res) {
    try {
      const app = await JobSimulationAppService.create(req);
      res.status(201).json(app);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: error.message || `Failed to create job simulation app` });
    }
  },

  async update(req, res) {
    try {
      const app = await JobSimulationAppService.update(req);
      res.json(app);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: error.message || `Failed to update job simulation app` });
    }
  },

  async deleteById(req, res) {
    try {
      const app = await JobSimulationAppService.deleteById(req.params.id, req.body);
      if (!app) {
        return res.status(404).json({ error: 'Job simulation app not found' });
      }
      res.json(app);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: error.message || `Failed to delete job simulation app` });
    }
  },

  async getAll(req, res) {
    try {
      const apps = await JobSimulationAppService.getAll();
      res.json(apps);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: error.message || `Failed to retrieve job simulation apps` });
    }
  },

  async getById(req, res) {
    try {
      const app = await JobSimulationAppService.getById(req.params.id);
      if (!app) {
        return res.status(404).json({ error: 'Job simulation app not found' });
      }
      res.json(app);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: error.message || `Failed to retrieve job simulation app` });
    }
  },
};

module.exports = JobSimulationAppController;
