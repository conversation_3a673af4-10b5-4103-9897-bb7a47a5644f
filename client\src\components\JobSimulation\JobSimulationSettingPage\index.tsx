import { useState } from 'react';
import AppCreation from './AppCreation';
import Screen from './Screens';

export default function JobSimulationSettingPage() {
  const [appSelected, setAppSelected] = useState();

  return (
    <div className="flex flex-col gap-4 p-8">
      <div className="flex h-full w-full text-white">
        <AppCreation setAppSelected={setAppSelected} appSelected={appSelected} />
      </div>

      {appSelected && (
        <div className="pt-4 text-white">
          <Screen />
        </div>
      )}
    </div>
  );
}
