const multer = require('multer');
const express = require('express');
const router = express.Router();
const { requireJwtAuth } = require('~/server/middleware');
const { storage } = require('~/server/routes/files/multer');
const JobSimulationAppScreenController = require('~/server/controllers/JobSimulationAppScreenController.js');

const uploadJobSimulation = multer({ storage });

router.get('', JobSimulationAppScreenController.getByUserAndApp);
router.get('/:id', JobSimulationAppScreenController.getById);

router.use(requireJwtAuth);

router.post(
  '',
  uploadJobSimulation.array('files', 12),
  JobSimulationAppScreenController.createMany,
);
router.patch('/order', JobSimulationAppScreenController.updateOrder);
router.patch('/:id', uploadJobSimulation.single('file'), JobSimulationAppScreenController.update);
router.delete('/:id', JobSimulationAppScreenController.deleteById);

module.exports = router;