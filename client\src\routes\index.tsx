import { memo } from 'react';
import { createBrowserRouter, Navigate, Outlet, useSearchParams } from 'react-router-dom';
import {
  ApiErrorWatcher,
  Login,
  Registration,
  RequestPasswordReset,
  ResetPassword,
  TwoFactorScreen,
  VerifyEmail,
} from '~/components/Auth';
import JobSimulationsPage from '~/components/JobSimulations';
import EmailDetailPage from '~/components/JobSimulations/EmailDetailPage';
import JobSimulationForm from '~/components/JobSimulations/JobSimulationForm';
import { AuthContextProvider } from '~/hooks/AuthContext';
import ChatRoute from './ChatRoute';
import dashboardRoutes from './Dashboard';
import jobSimulationRoutes from './JobSimulation';
import LoginLayout from './Layouts/Login';
import StartupLayout from './Layouts/Startup';
import Root from './Root';
import RouteErrorBoundary from './RouteErrorBoundary';
import Search from './Search';
import ShareRoute from './ShareRoute';
import TestImageClick from '~/components/TestImageClick';
import DashboardPage from '~/components/Dashboard';

const AuthLayout = memo(() => {
  const [searchParams] = useSearchParams();
  const jobSimulationId = searchParams.get('jobSimulationId');
  return (
    <AuthContextProvider
      authConfig={{
        isJobSimulation: jobSimulationId ? true : false,
        jobSimulationId: jobSimulationId || '',
        loginRedirect: '',
      }}
    >
      <Outlet />
      <ApiErrorWatcher />
    </AuthContextProvider>
  );
});

export const router = createBrowserRouter([
  {
    path: 'share/:shareId',
    element: <ShareRoute />,
    errorElement: <RouteErrorBoundary />,
  },
  {
    path: '/',
    element: <StartupLayout />,
    errorElement: <RouteErrorBoundary />,
    children: [
      {
        path: 'register',
        element: <Registration />,
      },
      {
        path: 'forgot-password',
        element: <RequestPasswordReset />,
      },
      {
        path: 'reset-password',
        element: <ResetPassword />,
      },
    ],
  },
  {
    path: 'verify',
    element: <VerifyEmail />,
    errorElement: <RouteErrorBoundary />,
  },
  jobSimulationRoutes,
  {
    path: '/job-simulations',
    element: <JobSimulationsPage />,
    errorElement: <RouteErrorBoundary />,
  },
  {
    path: '/test-image-click',
    element: <TestImageClick />,
    errorElement: <RouteErrorBoundary />,
  },
  {
    path: '/job-simulations/:jobId/edit',
    element: <JobSimulationForm />,
  },
  {
    path: '/job-simulations/create',
    element: <JobSimulationForm />,
  },
  {
    path: '/job-simulations/:jobId/emails/:emailId',
    element: <EmailDetailPage />,
  },
  {
    element: <AuthLayout />,
    errorElement: <RouteErrorBoundary />,
    children: [
      {
        path: '/',
        element: <LoginLayout />,
        children: [
          {
            path: 'login',
            element: <Login />,
          },
          {
            path: 'login/2fa',
            element: <TwoFactorScreen />,
          },
        ],
      },
      dashboardRoutes,
      {
        path: '/',
        element: <Root />,
        children: [
          {
            index: true,
            // element: <Navigate to="/c/new" replace={true} />,
            element: <Navigate to="/job-simulation/list" replace={true} />,
          },
          {
            path: 'c/:conversationId?',
            element: <ChatRoute />,
          },
          {
            path: 'search',
            element: <Search />,
          },
        ],
      },
    ],
  },
]);
