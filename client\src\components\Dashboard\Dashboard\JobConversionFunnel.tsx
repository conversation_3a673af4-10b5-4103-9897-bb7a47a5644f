import { dataService } from 'librechat-data-provider';
import { useEffect, useState } from 'react';
import { Cell, Funnel, Funnel<PERSON><PERSON>, LabelList, ResponsiveContainer, Tooltip } from 'recharts';
import { cn } from '~/utils';
import { Button } from '~/components/ui';

const listColors = ['#2563EB', '#3B82F6', '#93C5FD', '#F59E0B', '#EF4444'];
const defaultConversion = [
  {
    name: 'Started',
    value: 0,
    percentage: 0,
  },
  {
    name: 'Job Completed',
    value: 0,
    percentage: 0,
  },
  {
    name: 'Letter Issued',
    value: 0,
    percentage: 0.92,
  },
];

const buildConversion = (data: any[]) => {
  return data.map((item, index) => ({
    name: item.name,
    value: item.value,
    percentage: item.percentage,
    fill: listColors[index] || listColors[0],
  }));
};

const getConversionDataAsync = async (params: any): Promise<any> => {
  const result = await dataService.getEmployerDashboardConversion(params);
  //   const result = {
  //     status: 'success',
  //     data: [
  //       {
  //         name: 'Started',
  //         value: 52,
  //         percentage: 100,
  //       },
  //       {
  //         name: 'Job Completed',
  //         value: 13,
  //         percentage: 25,
  //       },
  //       {
  //         name: 'Letter Issued',
  //         value: 1,
  //         percentage: 1.92,
  //       },
  //     ],
  //   };
  return buildConversion(result?.data || []);
};

const getTopJobsAsync = async (params: any): Promise<any> => {
  const result = await dataService.getEmployerDashboardTopJobs(params);
  //   const result = {
  //     status: 'success',
  //     data: [
  //       {
  //         _id: '68193c186eae2c750ea99df4',
  //         name: 'Digital Marketing Analyst',
  //         jobSimulationId: 'digital-marketing',
  //       },
  //       {
  //         _id: '6818de396eae2c750ea99df3',
  //         name: 'ESG Analyst',
  //         jobSimulationId: 'esg-analyst',
  //       },
  //       {
  //         _id: '682407d5b5f1096fe4a7c35f',
  //         name: 'Marketing',
  //         jobSimulationId: 'lucas-test',
  //       },
  //     ],
  //   };
  return result?.data || [];
};

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;

    return (
      <div className="rounded-lg border border-gray-200 bg-white p-3 shadow-lg">
        <p className="font-medium text-gray-900">{data.name}</p>
        <p className="text-sm text-gray-600">
          {data.value.toLocaleString()} ({data.percentage}%)
        </p>
      </div>
    );
  }
  return null;
};

const JobConversionFunnel = () => {
  //   const [activeRange, setActiveRange] = useState<'day' | 'week' | 'month' | 'all'>('week');
  const [jobSimulationId, setJobSimulationId] = useState<string>('alljobs');
  const [jobSimulations, setJobSimulations] = useState<{ jobSimulationId: string; name: string }[]>(
    [],
  );

  const [conversion, setConversion] = useState<any[]>(defaultConversion);
  const [isGettingData, setIsGettingData] = useState(false);

  useEffect(() => {
    getTopJobsAsync({ limit: 5 }).then((data) => {
      setJobSimulations([{ jobSimulationId: 'alljobs', name: 'All' }, ...data]);
    });
  }, []);

  useEffect(() => {
    if (isGettingData) return;
    setIsGettingData(true);
    getConversionDataAsync({
      jobSimulationId: jobSimulationId === 'alljobs' ? undefined : jobSimulationId,
    })
      .then((data) => {
        setConversion(data);
      })
      .finally(() => {
        setIsGettingData(false);
      });
  }, [jobSimulationId]);

  return (
    <div className="flex flex-col rounded-xl bg-white p-6 text-[#505050] drop-shadow-lg">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-xl font-semibold">Job Simulation Conversion Funnel</h3>
        {/* <div className="flex space-x-2 rounded-full bg-[#F8F8FF] px-2 py-1">
          {['day', 'week', 'month', 'all'].map((range) => (
            <Button
              key={range}
              size="sm"
              onClick={() => setActiveRange(range as 'day' | 'week' | 'month' | 'all')}
              className={cn(
                'rounded-full text-xs capitalize',
                activeRange === range
                  ? 'bg-gray-900 text-white'
                  : 'bg-transparent text-gray-600 hover:bg-transparent',
              )}
            >
              {range}
            </Button>
          ))}
        </div> */}
      </div>
      <hr />
      <div className="my-4 flex flex-wrap">
        {jobSimulations.map((jobSimulation) => (
          <Button
            key={jobSimulation.jobSimulationId}
            size="sm"
            onClick={() => {
              if (isGettingData) return;
              setJobSimulationId(jobSimulation.jobSimulationId);
            }}
            disabled={isGettingData}
            className={cn(
              jobSimulationId === jobSimulation.jobSimulationId
                ? 'bg-blue-100 font-light text-blue-600 hover:bg-blue-100'
                : 'bg-transparent font-light text-gray-500 hover:bg-transparent',
            )}
          >
            {jobSimulation.name}
          </Button>
        ))}
      </div>
      <div className="mt-4 w-full flex-1">
        <div className="flex justify-center">
          <div className="h-80 max-w-3xl">
            <ResponsiveContainer width="100%" minWidth={500} height={360}>
              <FunnelChart>
                <Tooltip content={<CustomTooltip />} />
                <Funnel dataKey="value" data={conversion} isAnimationActive>
                  <LabelList position="center" fill="#fff" stroke="none" fontSize={14} />
                  {conversion.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.fill} />
                  ))}
                </Funnel>
              </FunnelChart>
            </ResponsiveContainer>
          </div>
        </div>
        <div className="mt-4 flex justify-between">
          {conversion.map((stage, index) => {
            return (
              <div key={stage.name} className="text-center">
                <div className="mb-2 text-sm font-medium text-gray-600">{stage.name}</div>
                <div className="mb-1 text-xl font-bold text-gray-900">
                  {stage.value.toLocaleString()}
                </div>
                <div className="text-sm text-gray-500">{stage.percentage}%</div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default JobConversionFunnel;
