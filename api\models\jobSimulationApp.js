const mongoose = require('mongoose');
const { jobSimulationAppSchema } = require('@librechat/data-schemas');

const JobSimulationApp = mongoose.model('jobSimulationApp', jobSimulationAppSchema);

/** * Create a new job simulation app
 * @param {Object} params - Parameters for the new app
 * @returns {Promise<Object>} - Created app object
 */
const create = async function (params) {
  try {
    params.role = (params.role || 'admin').toUpperCase();
    const app = await JobSimulationApp.create(params);

    return app;
  } catch (error) {
    throw new Error(`Failed to create job simulation app: ${error.message}`);
  }
};

/** * Update an existing job simulation app
 * @param {string} id - App ID to update
 * @param {Object} params - Parameters to update
 * @returns {Promise<Object>} - Updated app object
 */
const update = async function (id, params) {
  try {
    const app = await JobSimulationApp.findOneAndUpdate(
      { _id: id, userId: params.userId },
      { $set: params },
      { new: true },
    ).lean();

    return app;
  } catch (error) {
    throw new Error(`Failed to update job simulation app: ${error.message}`);
  }
};

/**
 * Delete a job simulation app by ID
 * @param {string} id - App ID to delete
 * @param {Object} params - Parameters to delete
 * @returns {Promise<Object>} - Deleted app object or null if not found
 */
const deleteById = async function (id, params) {
  try {
    const app = await JobSimulationApp.findOneAndDelete({ _id: id, userId: params.userId }).lean();

    return app;
  } catch (error) {
    throw new Error(`Failed to delete job simulation app: ${error.message}`);
  }
};

/**
 * Get all job simulation apps
 * @returns {Promise<Array>} - Array of app objects
 */
const getAll = async function () {
  try {
    return await JobSimulationApp.find({}).sort({ _id: -1 }).lean();
  } catch (error) {
    throw new Error(`Failed to retrieve job simulation apps: ${error.message}`);
  }
};
/**
 * Get a job simulation app by ID
 * @param {string} id - App ID to retrieve
 * @returns {Promise<Object>} - App object
 */
const getById = async function (id) {
  try {
    const app = await JobSimulationApp.findById(id).lean();

    return app;
  } catch (error) {
    throw new Error(`Failed to retrieve job simulation app: ${error.message}`);
  }
};

module.exports = {
  model: JobSimulationApp,
  create,
  update,
  deleteById,
  getAll,
  getById,
};
