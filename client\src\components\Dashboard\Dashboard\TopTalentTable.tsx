import { dataService } from 'librechat-data-provider';
import { useEffect, useState } from 'react';
import { cn } from '~/utils';
import { Button, Tag, TooltipAnchor } from '~/components/ui';

const medals = ['🥇', '🥈', '🥉'];

const minutesToHours = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours}h ${mins}m`;
};

const buildProfiles = (data: any[]) => {
  return data.map((item, index) => ({
    rank: index + 1,
    name: item.user?.name,
    email: item.user?.email,
    id: item.user?.id,
    scores: item.scores,
    skills: item.skills,
    jobSimulationName: item.jobSimulation?.name,
    completionTime: minutesToHours(item.completionMins || 0),
    medal: medals[index] || (index + 1).toString(),
  }));
};

const getTopProfilesAsync = async (params: any): Promise<any> => {
  const result = await dataService.getEmployerDashboardOverviewTopProfiles(params);
  return buildProfiles(result?.data || []);
};

const getTopJobsAsync = async (params: any): Promise<any> => {
  const result = await dataService.getEmployerDashboardTopJobs(params);
  //   const result = {
  //     status: 'success',
  //     data: [
  //       {
  //         _id: '68193c186eae2c750ea99df4',
  //         name: 'Digital Marketing Analyst',
  //         jobSimulationId: 'digital-marketing',
  //       },
  //       {
  //         _id: '6818de396eae2c750ea99df3',
  //         name: 'ESG Analyst',
  //         jobSimulationId: 'esg-analyst',
  //       },
  //       {
  //         _id: '682407d5b5f1096fe4a7c35f',
  //         name: 'Marketing',
  //         jobSimulationId: 'lucas-test',
  //       },
  //     ],
  //   };
  return result?.data || [];
};

const TopTalentTable = () => {
  const [jobSimulationId, setJobSimulationId] = useState<string>('alljobs');
  const [jobSimulations, setJobSimulations] = useState<{ jobSimulationId: string; name: string }[]>(
    [],
  );
  const [profiles, setProfiles] = useState<
    {
      rank: number;
      name: string;
      email: string;
      id: string;
      jobSimulationName: string;
      completionTime: string;
      scores: number;
      skills?: { name: string; rating: number }[];
      medal: string;
    }[]
  >([]);
  const [isGettingData, setIsGettingData] = useState(false);

  useEffect(() => {
    getTopJobsAsync({ limit: 5 }).then((data) => {
      setJobSimulations([{ jobSimulationId: 'alljobs', name: 'All' }, ...data]);
    });
  }, []);

  useEffect(() => {
    if (isGettingData) return;
    setIsGettingData(true);
    getTopProfilesAsync({
      jobSimulationId: jobSimulationId === 'alljobs' ? undefined : jobSimulationId,
      limit: 10,
    })
      .then((data) => {
        setProfiles(data);
      })
      .finally(() => {
        setIsGettingData(false);
      });
  }, [jobSimulationId]);

  return (
    <div className="rounded-xl bg-white p-6 text-[#505050] drop-shadow-lg">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-xl font-semibold">Top Talent Profiles</h3>
      </div>
      <hr />
      <div className="my-4 flex flex-wrap">
        {jobSimulations.map((jobSimulation) => (
          <Button
            key={jobSimulation.jobSimulationId}
            size="sm"
            onClick={() => {
              if (isGettingData) return;
              setJobSimulationId(jobSimulation.jobSimulationId);
            }}
            disabled={isGettingData}
            className={cn(
              jobSimulationId === jobSimulation.jobSimulationId
                ? 'bg-blue-100 font-light text-blue-600 hover:bg-blue-100'
                : 'bg-transparent font-light text-gray-500 hover:bg-transparent',
            )}
          >
            {jobSimulation.name}
          </Button>
        ))}
      </div>
      <div className="mt-4 w-full flex-1 overflow-x-auto">
        <table className="w-full">
          <thead className="border-b border-gray-200">
            <tr className="text-left">
              <th className="pb-3 text-sm font-light text-gray-500 opacity-60">Top</th>
              <th className="pb-3 text-sm font-light text-gray-500 opacity-60">Candidate Name</th>
              <th className="pb-3 text-sm font-light text-gray-500 opacity-60">Job Simulation</th>
              <th className="pb-3 text-sm font-light text-gray-500 opacity-60">Skills</th>
              <th className="pb-3 text-sm font-light text-gray-500 opacity-60">Score</th>
              <th className="pb-3 text-sm font-light text-gray-500 opacity-60">Completion Time</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {profiles.map((profile) => (
              <tr key={profile.rank} className="hover:bg-gray-50">
                <td className="max-w-16 py-4">
                  <span className="font-medium text-gray-500">{profile.medal}</span>
                </td>
                <td className="py-4">
                  <div>
                    <div className="text-sm font-medium text-gray-600">{profile.name}</div>
                    <div className="text-sm text-gray-500">{profile.email}</div>
                  </div>
                </td>
                <td className="py-4 max-w-36">
                  <div className="text-sm font-medium text-gray-600">
                    {profile.jobSimulationName}
                  </div>
                </td>
                <td className="min-w-56 py-4">
                  <TooltipAnchor
                    description={(profile.skills || [])?.map((skill) => skill.name).join(', ')}
                    side="right"
                    toolTipClassName="max-w-[400px]"
                    render={
                      <div className="flex max-w-52 flex-col space-y-2 text-sm font-medium text-gray-600">
                        {(profile.skills || []).slice(0, 3).map((skill) => (
                          <div key={skill.name} className="truncate">
                            {skill.name}
                          </div>
                        ))}
                        {(profile.skills || []).length > 3 && <div className="">...</div>}
                      </div>
                    }
                  />
                </td>
                <td className="max-w-16 py-4">
                  <div className="text-sm font-medium text-gray-600">{profile.scores || '-'}</div>
                </td>
                <td className="py-4">
                  <div className="text-sm font-medium text-gray-600">{profile.completionTime}</div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        <hr />
      </div>

      <div className="mt-6 flex justify-center">
        <Button variant="link" className="font-light italic text-gray-600 underline">
          View detail
        </Button>
      </div>
    </div>
  );
};

export default TopTalentTable;
