import { Document, Schema } from 'mongoose';

export interface IJobSimulationProgress extends Document {
  jobSimulationId: string;
  email: string;

  // TODO: Allow user to reset the progress (!?) Not implemented yet
  sessionId: string;
  userId: string;
  intakeId?: string;
  emails: {
    id: string;
    read: boolean;
    time: number;
  }[];
  skills?: {
    name: string;
    rating: number;
  }[];
  enabledApps?: string[];
  status: string;
  conversationId?: string;

  // TODO: For tracking - statistics. Move to another collection (!?)
  claimedCertification?: boolean;
  downloadCertification?: boolean;
  totalTasks?: number;
  totalCompletedTasks?: number;
  completionMins?: number;
  scores?: number;
  tasks?: {
    id: string;
    title: string;
    feedback?: string;
    status: string;
    completedAt: Date;
  };
  completedAt?: Date;
}

const jobSimulationProgressSchema: Schema<IJobSimulationProgress> = new Schema(
  {
    jobSimulationId: {
      type: String,
      required: true,
    },
    email: {
      type: String,
      required: true,
    },
    userId: {
      type: String,
      required: true,
    },
    intakeId: {
      type: String,
      required: false,
    },
    status: {
      type: String,
      required: true,
    },
    sessionId: {
      type: String,
      required: true,
      unique: true,
    },
    emails: {
      type: [
        {
          id: {
            type: String,
            required: true,
          },
          read: {
            type: Boolean,
            required: true,
          },
          time: {
            type: Number,
            required: true,
          },
        },
      ],
      default: undefined,
    },
    skills: {
      type: [
        {
          name: {
            type: String,
            required: true,
          },
          rating: {
            type: Number,
            required: true,
          },
        },
      ],
      default: undefined,
    },
    enabledApps: {
      type: [String],
      required: false,
      default: ['mail', 'news', 'facebook-ad', 'fb-ad', 'google-ads'],
    },
    conversationId: {
      type: String,
      required: false,
    },
    totalTasks: {
      type: Number,
      required: false,
    },
    totalCompletedTasks: {
      type: Number,
      required: false,
    },
    completionMins: {
      type: Number,
      required: false,
    },
    scores: {
      type: Number,
      required: false,
    },
    tasks: {
      type: [
        {
          id: {
            type: String,
            required: true,
          },
          title: {
            type: String,
            required: true,
          },
          feedback: {
            type: String,
            required: false,
          },
          completedAt: {
            type: Date,
            required: false,
          },
        },
      ],
      default: undefined,
    },
    claimedCertification: {
      type: Boolean,
      required: false,
    },
    downloadCertification: {
      type: Boolean,
      required: false,
    },
    completedAt: {
      type: Date,
      required: false,
    },
  },
  { timestamps: true },
);

export default jobSimulationProgressSchema;
