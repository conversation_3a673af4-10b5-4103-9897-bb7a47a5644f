import { Document, Schema } from 'mongoose';

enum JobSimulationType {
    PUBLIC = 'public',
    PRIVATE = 'private'
}

export interface IJobSimulationApp extends Document {
  name: string;
  icon: string;
  type: JobSimulationType;
  userId: string;
  role: string;
}

const jobSimulationAppSchema: Schema<IJobSimulationApp> = new Schema({
  name: {
    type: String,
    required: true,
  },
  icon: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    enum: [JobSimulationType.PUBLIC, JobSimulationType.PRIVATE],
    required: true,
  },
  userId: {
    type: String,
    required: true,
  },
  role: {
    type: String,
    required: true,
  },
}, {
  timestamps: true
});

export default jobSimulationAppSchema;