const JobSimulationStatisticService = require('~/server/services/JobSimulation/JobSimulationStatisticService');
// const { logger } = require('~/config');

const JobSimulationStatisticController = {

  async getOverview(req, res) {
    try {
      const data = await JobSimulationStatisticService.getEmployerOverview();
      res.json({
        status: 'success',
        data,
      });
    } catch (error) {
      console.error('=== ERROR RETRIEVING OVERVIEW DATA ===', error);
      res.status(500).json({ error: 'Failed to retrieve overview' });
    }
  },

  async getTopJobs(req, res) {
    try {
      const data = await JobSimulationStatisticService.getTopJobs(req.query || {});
      res.json({
        status: 'success',
        data,
      });
    } catch (error) {
      console.error('=== ERROR RETRIEVING TOP JOBS ===', error);
      res.status(500).json({ error: 'Failed to retrieve top jobs' });
    }
  },

  async getTopPerformingJobs(req, res) {
    try {
      const data = await JobSimulationStatisticService.getTopPerformingJobs(req.query || {});
      res.json({
        status: 'success',
        data,
      });
    } catch (error) {
      console.error('=== ERROR RETRIEVING TOP PERFORMING JOBS ===', error);
      res.status(500).json({ error: 'Failed to retrieve top performing jobs' });
    }
  },

  async getConversion(req, res) {
    try {
      const data = await JobSimulationStatisticService.getConversion(req.query || {});
      res.json({
        status: 'success',
        data,
      });
    } catch (error) {
      console.error('=== ERROR RETRIEVING CONVERSION DATA ===', error);
      res.status(500).json({ error: 'Failed to retrieve conversion data' });
    }
  },

  async getOverviewTopProfiles(req, res) {
    try {
      const data = await JobSimulationStatisticService.getOverviewTopProfiles(req.query || {});
      res.json({
        success: true,
        data,
      });
    } catch (error) {
      console.error('=== ERROR RETRIEVING TOP PROFILES ===', error);
      res.status(500).json({ error: 'Failed to retrieve top profiles' });
    }
  },

  async getJobOverview(req, res) {
    try {
      const data = await JobSimulationStatisticService.getJobOverview(req.query || {});
      res.json({
        success: true,
        data,
      });
    } catch (error) {
      console.error('=== ERROR RETRIEVING JOB OVERVIEW ===', error);
      res.status(500).json({ error: 'Failed to retrieve job overview' });
    }
  },
};

module.exports = JobSimulationStatisticController;