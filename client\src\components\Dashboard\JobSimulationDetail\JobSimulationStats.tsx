import { label } from 'librechat-data-provider/dist/types';
import { StarIcon } from '~/components/svg';

const minutesToHours = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours}h ${mins}m`;
};

const JobSimulationStats = ({
  overview,
}: {
  overview: {
    totalParticipants: number;
    totalCompletedJobs: number;
    avgFeedbackScores: number;
    totalAttempts: number;
    avgCompletionMins: number;
    avgCompletionRate: number;
    minCompletionMins: number;
  } | null;
}) => {
  return (
    <div className="mb-6 grid grid-cols-2 gap-2 lg:grid-cols-5">
      <div className="rounded-xl bg-[#F7F7F7] p-4">
        <div className="mb-2 text-sm font-medium text-[#505050]">Total Participants</div>
        <div className="flex items-center">
          <span className="text-2xl font-bold text-gray-900">{overview?.totalParticipants}</span>
          <span className="ml-1 text-sm text-gray-500">Users</span>
        </div>
      </div>
      {/* <div className="rounded-xl bg-[#F7F7F7] p-4">
        <div className="mb-2 text-sm font-medium text-[#505050]">Total Attemps</div>
        <div className="flex items-center">
          <span className="text-2xl font-bold text-gray-900">{overview?.totalAttempts}</span>
          <span className="ml-1 text-sm text-gray-500">Attemps</span>
        </div>
      </div> */}
      <div className="rounded-xl bg-[#F7F7F7] p-4">
        <div className="mb-2 text-sm font-medium text-[#505050]">Total Completions</div>
        <div className="flex items-center">
          <span className="text-2xl font-bold text-gray-900">{overview?.totalCompletedJobs}</span>
          <span className="ml-1 text-sm text-gray-500">Simulations</span>
        </div>
      </div>
      <div className="rounded-xl bg-[#F7F7F7] p-4">
        <div className="mb-2 text-sm font-medium text-[#505050]">AVG. Completion Rate</div>
        <div className="flex items-center">
          <span className="text-2xl font-bold text-gray-900">{overview?.avgCompletionRate}</span>
          <span className="ml-1 text-sm text-gray-500">%</span>
        </div>
      </div>
      <div className="rounded-xl bg-[#F7F7F7] p-4">
        <div className="mb-2 text-sm font-medium text-[#505050]">AVG. Completion Time</div>
        <div className="flex items-center">
          <span className="text-2xl font-bold text-gray-900">
            {minutesToHours(Math.round(overview?.avgCompletionMins || 0))}
          </span>
          {/* <span className="ml-1 text-sm text-gray-500">/hours</span> */}
        </div>
      </div>
      <div className="rounded-xl bg-[#F7F7F7] p-4">
        <div className="mb-2 text-sm font-medium text-[#505050]">MIN. Completion Time</div>
        <div className="flex items-center">
          <span className="mr-1">
            <StarIcon />
          </span>
          <span className="text-2xl font-bold text-gray-900">
            {minutesToHours(Math.round(overview?.minCompletionMins || 0))}
          </span>
        </div>
      </div>
    </div>
  );
};

export default JobSimulationStats;
