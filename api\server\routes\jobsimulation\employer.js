const express = require('express');
const router = express.Router();
const JobSimulationStatisticController = require('~/server/controllers/JobSimulationStatisticController');
const { requireJwtAuth } = require('~/server/middleware');

// router.use(requireJwtAuth);
// TODO: add middleware check role EMPLOYER (api\server\middleware\roles\checkEmployer.js)

router.get('/dashboard/overview', JobSimulationStatisticController.getOverview);
router.get('/dashboard/top-performing-jobs', JobSimulationStatisticController.getTopPerformingJobs);
router.get('/dashboard/top-jobs', JobSimulationStatisticController.getTopJobs);
router.get('/dashboard/conversion', JobSimulationStatisticController.getConversion);
router.get('/dashboard/overview-top-profiles', JobSimulationStatisticController.getOverviewTopProfiles);
router.get('/dashboard/job-overview', JobSimulationStatisticController.getJobOverview);


module.exports = router;
