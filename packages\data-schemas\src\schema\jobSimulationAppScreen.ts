import { Document, Schema } from 'mongoose';

interface ElementAction {
  type: string;
  dataContextId?: string;
  screenId?: string;
  inputTextType?: string;
  dataContextLabel?: string;
  message?: string;
}

interface Element {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
  title: string;
  backgroundColor?: string;
  border?: string;
  actions: ElementAction[];
}

interface ChartLine {
  name: string;
  dataContextId?: string;
  type: string;
  stroke?: string;
  staticValues?: string[];
}

interface ChartData {
  labelX?: string;
  labelY?: string;
  timePoints: string[];
  xLabels: string[];
  lines: ChartLine[];
}

interface Chart {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
  title: string;
  type: string;
  data: ChartData;
}

interface PlaceholderTimeData {
  name: string;
  value: string;
}

interface PlaceholderStyle {
  backgroundColor?: string;
  border?: string;
  fontSize?: string;
  fontWeight?: string;
  color?: string;
  whiteSpace?: string;
  overflow?: string;
  alignItems?: string;
  textOverflow?: string;
}

interface Placeholder {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
  title: string;
  dataContextId?: string;
  style?: PlaceholderStyle;
  dataByTime?: PlaceholderTimeData[];
}

export interface IJobSimulationAppScreen extends Document {
  userId: string;
  appId: string;
  order: number;
  image: string;
  bgColor?: string;
  elements?: Element[];
  charts?: Chart[];
  placeholders?: Placeholder[];
}

const jobSimulationAppScreenSchema: Schema<IJobSimulationAppScreen> = new Schema(
  {
    userId: {
      type: String,
      required: true,
    },
    appId: {
      type: String,
      required: true,
    },
    order: {
      type: Number,
      required: true,
    },
    image: {
      type: String,
      required: true,
    },
    bgColor: {
      type: String,
      required: false,
    },
    elements: {
      type: [
        {
          x1: {
            type: Number,
            required: true,
          },
          y1: {
            type: Number,
            required: true,
          },
          x2: {
            type: Number,
            required: true,
          },
          y2: {
            type: Number,
            required: true,
          },
          title: {
            type: String,
            required: true,
          },
          backgroundColor: {
            type: String,
            required: false,
          },
          border: {
            type: String,
            required: false,
          },
          actions: {
            type: [
              {
                type: {
                  type: String,
                  required: true,
                },
                dataContextId: {
                  type: String,
                  required: false,
                },
                screenId: {
                  type: String,
                  required: false,
                },
                inputTextType: {
                  type: String,
                  required: false,
                },
                dataContextLabel: {
                  type: String,
                  required: false,
                },
                message: {
                  type: String,
                  required: false,
                },
              },
            ],
            default: [],
          },
        },
      ],
      required: false,
      default: [],
    },
    charts: {
      type: [
        {
          x1: {
            type: Number,
            required: true,
          },
          y1: {
            type: Number,
            required: true,
          },
          x2: {
            type: Number,
            required: true,
          },
          y2: {
            type: Number,
            required: true,
          },
          title: {
            type: String,
            required: true,
          },
          type: {
            type: String,
            required: true,
          },
          data: {
            type: {
              labelX: {
                type: String,
                required: false,
              },
              labelY: {
                type: String,
                required: false,
              },
              timePoints: {
                type: [String],
                required: true,
              },
              xLabels: {
                type: [String],
                required: true,
              },
              lines: {
                type: [
                  {
                    name: {
                      type: String,
                      required: true,
                    },
                    dataContextId: {
                      type: String,
                      required: false,
                    },
                    type: {
                      type: String,
                      required: true,
                    },
                    stroke: {
                      type: String,
                      required: false,
                    },
                    staticValues: {
                      type: [String],
                      required: false,
                    },
                  },
                ],
                default: [],
              },
            },
            required: true,
          },
        },
      ],
      required: false,
      default: [],
    },
    placeholders: {
      type: [
        {
          x1: {
            type: Number,
            required: true,
          },
          y1: {
            type: Number,
            required: true,
          },
          x2: {
            type: Number,
            required: true,
          },
          y2: {
            type: Number,
            required: true,
          },
          title: {
            type: String,
            required: true,
          },
          dataContextId: {
            type: String,
            required: false,
          },
          style: {
            type: {
              backgroundColor: {
                type: String,
                required: false,
              },
              border: {
                type: String,
                required: false,
              },
              fontSize: {
                type: String,
                required: false,
              },
              fontWeight: {
                type: String,
                required: false,
              },
              color: {
                type: String,
                required: false,
              },
              whiteSpace: {
                type: String,
                required: false,
              },
              overflow: {
                type: String,
                required: false,
              },
              alignItems: {
                type: String,
                required: false,
              },
              textOverflow: {
                type: String,
                required: false,
              },
            },
            required: false,
          },
          dataByTime: {
            type: [
              {
                name: {
                  type: String,
                  required: true,
                },
                value: {
                  type: String,
                  required: true,
                },
              },
            ],
            required: false,
            default: [],
          },
        },
      ],
      required: false,
      default: [],
    },
  },
  {
    timestamps: true,
  },
);

export default jobSimulationAppScreenSchema;
