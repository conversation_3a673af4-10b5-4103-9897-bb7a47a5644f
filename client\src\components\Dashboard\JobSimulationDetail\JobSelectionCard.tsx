import { Button } from "~/components/ui";
import { cn } from "~/utils";

interface JobSelectionCardProps {
	selectedJob: string;
	onSelect: (job: string) => void;
}

const jobs = [
	{ id: 'esg-analyst', label: 'ESG Analyst' },
	{ id: 'digital-marketing', label: 'Digital Marketing' },
];

const JobSelectionCard = ({ selectedJob, onSelect }: JobSelectionCardProps) => {

	return (
		<div className="bg-white p-4 rounded-xl drop-shadow-lg text-[#505050]">
			<h3 className="text-sm mb-4 text-center">Choose Job Simulation</h3>

			<div className="space-y-3">
				{jobs.map(({ id, label }) => (
					<div
						key={id}
						onClick={() => onSelect(id)}
						className={cn(
							"cursor-pointer w-full h-[90px] flex items-center justify-start p-6 rounded-xl border active:bg-transparent active:shadow-none focus:ring-0 focus-visible:ring-0 focus:outline-none ring-0",
							selectedJob === id
								? "bg-gradient-to-b from-[#B9F3FF] to-[#34A1F4] border-[#34A1F4] text-white font-medium"
								: "bg-white border-[#F2F3F7] text-[#505050] font-normal opacity-70"
						)}
					>
						<div className="w-16 h-9 rounded mr-3 flex items-center justify-center">
							<img src='/assets/job-simulation/logo-js.png' className="w-full h-full" />
						</div>
						<span className="font-medium">{label}</span>
					</div>
				))}
			</div>
		</div>
	);
};

export default JobSelectionCard;