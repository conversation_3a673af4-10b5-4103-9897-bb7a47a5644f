import { useEffect, useRef, useState } from 'react';
import { Button, Dialog, Input, SelectDropDown, OGDialogTemplate } from '~/components/ui';
import { useForm, FormProvider, Controller, useWatch } from 'react-hook-form';
import type t from 'librechat-data-provider';
import { PlusIcon } from '~/components/svg';

interface IProps {
  open: boolean;
  onClose: () => void;
  app?: t.TJobSimulationApp | null;
  type: string;
  onOk: (type: string, data) => void;
  disabled?: boolean;
}

export default function AppModal({ open, onClose, app, type, onOk, disabled }: IProps) {
  const [selectedIcon, setSelectedIcon] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    clearErrors,
  } = useForm({
    defaultValues: {
      name: type === 'edit' && app ? app.name : '',
      file: undefined as unknown as File,
    },
  });

  const handleIconChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      setValue('file', undefined as unknown as File);
      setSelectedIcon(null);
      setPreviewUrl(null);
      return;
    }

    setSelectedIcon(file);
    setValue('file', file);
    clearErrors('file');

    const reader = new FileReader();
    reader.onloadend = () => {
      setPreviewUrl(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleIconClick = () => {
    fileInputRef.current?.click();
  };

  const onSubmit = (data: any) => {
    const formData = new FormData();
    formData.append('name', data.name);
    if (data.file) {
      formData.append('file', data.file);
    }
    if (typeof onOk === 'function') {
      onOk(type, data);
    }
  };

  useEffect(() => {
    if (type === 'edit' && app) {
      // setValue(
      //   'file',
      //   app.icon ? new File([app.icon], 'icon.png') : (undefined as unknown as File),
      // );
      setValue('name', app.name || '');
      setSelectedIcon(app.icon ? new File([app.icon], 'icon.png') : null);
      setPreviewUrl(app.icon || null);
    }
  }, [type, app]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <OGDialogTemplate
        className="w-full max-w-xl"
        title={`${type === 'edit' ? 'Edit' : 'Create New'} App`}
        main={
          <div className="flex flex-col gap-4 text-white">
            <div>
              <Input
                type="text"
                placeholder="Enter name"
                className="w-full border border-[#424242]"
                {...register('name', {
                  required: 'Name is required',
                  maxLength: {
                    value: 100,
                    message: 'Name cannot exceed 100 characters',
                  },
                })}
              />
              {errors?.name && (
                <span className="text-xs text-red-500">{errors?.name?.message}</span>
              )}
            </div>
            <div>
              <label className="mb-2 block text-sm font-medium">
                App Icon <span className="text-red-500">*</span>
              </label>
              <div
                className="flex h-32 w-32 cursor-pointer flex-col items-center justify-center rounded-lg border border-dashed border-gray-400 bg-gray-800 hover:border-purple-500"
                onClick={handleIconClick}
              >
                {previewUrl ? (
                  <img src={previewUrl} alt="Icon preview" className="cover h-16 w-16 rounded-lg" />
                ) : (
                  <>
                    <PlusIcon className="mb-2 h-8 w-8 text-gray-400" />
                    <p className="text-xs text-gray-400">Click to upload icon</p>
                    <p className="mt-1 text-xs text-gray-500">PNG, JPG, SVG</p>
                  </>
                )}
                <input
                  type="file"
                  className="hidden"
                  accept="image/*"
                  {...register('file', {
                    required: type === 'edit' ? false : 'Icon is required',
                  })}
                  onChange={handleIconChange}
                  ref={fileInputRef}
                />
              </div>
              {errors?.file && (
                <span className="mt-1 block text-xs text-red-500">{errors?.file?.message}</span>
              )}
              {selectedIcon && (
                <p className="mt-2 text-xs text-gray-400">
                  Selected: {selectedIcon.name} ({Math.round(selectedIcon.size / 1024)} KB)
                </p>
              )}
            </div>
          </div>
        }
        buttons={
          <div>
            <Button onClick={handleSubmit(onSubmit)} disabled={disabled}>
              OK
            </Button>
          </div>
        }
      />
    </Dialog>
  );
}
