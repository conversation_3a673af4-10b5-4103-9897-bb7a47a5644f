import React, { useState } from 'react';
import { CheckCircle, X, Info } from 'lucide-react';
import { Button, Dialog, DialogContent, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, Slider } from '~/components/ui';

interface PublishSuccessModalProps {
	isOpen: boolean;
	onClose: () => void;
	id: number,
	postText: string,
	selectedImages: any,
	selectedAudience: string,
	boostEnabled: boolean,
	logo: string
	onGoToAdDetail: any
}

const PublishSuccessModal: React.FC<PublishSuccessModalProps> = ({
	isOpen,
	onClose,
	id,
	postText,
	selectedImages,
	selectedAudience,
	boostEnabled,
	logo,
	onGoToAdDetail
}) => {
	const [adGoal, setAdGoal] = useState('page-likes');
	const [dailyBudget, setDailyBudget] = useState(250000);
	const [isLoading, setIsLoading] = useState(false);

	const adGoalOptions = [
		{
			value: 'page-likes',
			label: 'Get more Page likes',
			description: 'Create a promotion to help more people find and like your Page.',
			icon: '📄'
		},
		{
			value: 'calls',
			label: 'Get more calls',
			description: 'Show your ad to people who are likely to call your business.',
			icon: '📞'
		},
	];
	const minBudget = 60000
	const maxBudget = 2000000;
	const selectedGoal = adGoalOptions.find(option => option.value === adGoal);

	const calculateAccountsReached = (budget: number) => {
		const minReach = Math.round((budget / 60000) * 1200 + 6000);
		const maxReach = Math.round((budget / 60000) * 3500 + 17000);
		return { min: minReach, max: maxReach };
	};

	const calculatePageLikes = (budget: number) => {
		const minLikes = Math.round((budget / 60000) * 40 + 160);
		const maxLikes = Math.round((budget / 60000) * 115 + 460);
		return { min: minLikes, max: maxLikes };
	};

	const accountsReached = calculateAccountsReached(dailyBudget);
	const pageLikes = calculatePageLikes(dailyBudget);

	const formatNumber = (num: number) => {
		if (num >= 1000) {
			return `${(num / 1000).toFixed(1)}K`;
		}
		return num.toString();
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-4xl p-0 max-h-[90vh] overflow-hidden">
				{/* Header */}
				<div className="flex items-center justify-between p-4 border-b">
					<div className="flex items-center space-x-3">
						<CheckCircle className="w-6 h-6 text-green-500" />
						<h2 className="text-xl font-semibold">Your post is published</h2>
					</div>
				</div>

				<div className="p-4 pt-0 space-y-6">
					<div>
						<p className="text-lg font-medium">Reach a wider audience by creating an ad from your post</p>
						<p className="text-sm">Adjust the ad goal and daily budget to view estimated advertising results.</p>
					</div>

					<div className="grid grid-cols-2 gap-8">
						{/* Left Column - Ad Settings */}
						<div className="space-y-6">
							<div className='space-y-2'>
								<label className="text-sm font-medium">Ad goal</label>
								<Select value={adGoal} onValueChange={setAdGoal}>
									<SelectTrigger className="w-full">
										<SelectValue placeholder={selectedGoal?.label} />
									</SelectTrigger>
									<SelectContent className="w-full max-w-md bg-white">
										{adGoalOptions.map((option) => (
											<SelectItem key={option.value} value={option.value} className="p-2 pl-8">
												{option.label}
												{/* <div className="flex items-center space-x-3">
													<div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
														<span className="text-xs">{option.icon}</span>
													</div>
													<div className="font-medium text-gray-900">{option.label}</div>
												</div> */}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>

							<div className="space-y-4">
								<div className="text-center">
									<div className="text-2xl font-bold text-blue-500 flex items-center justify-center gap-1"><span className='text-base'>₫</span>{dailyBudget.toLocaleString()}</div>
									<div className="text-sm text-[#929294]">Daily budget</div>
								</div>

								<div className="flex items-center gap-3">
									<span className="text-sm text-[#929294]">₫{minBudget.toLocaleString()}</span>
									<Slider
										value={[dailyBudget]}
										onValueChange={(value) => setDailyBudget(value[0])}
										max={maxBudget}
										min={minBudget}
										step={10000}
										className="w-full"
									/>
									<span className="text-sm text-[#929294]">₫{maxBudget.toLocaleString()}</span>
								</div>
							</div>
						</div>

						{/* Right Column - Preview & Results */}
						<div className="space-y-6">
							<div className="flex items-start space-x-3">
								<div className="w-24 h-24 bg-gray-300 relative">
									<img src={selectedImages[0]?.preview} className='rounded-full w-full h-full' />
									<img src="https://www.spokanefoodpolicy.org/wp-content/uploads/sites/8/2021/08/facebook-logo-2019.png" className='w-6 h-6 absolute -bottom-1 -right-1 border-2 border-white rounded-full' />
								</div>
								<div className="flex-1">
									<p className="font-medium">
										Demo
									</p>
									<p className="text-sm">Jun 3, 2025, 9:39 AM</p>
									<p className="text-sm text-gray-400">ID: {id}</p>
								</div>
							</div>

							<div className='bg-[#f2f4f7] p-4'>
								<p className="text-lg font-medium">Estimated daily results</p>
								<p className="text-xs text-gray-500 mb-3">From advertising</p>

								<div className="space-y-3">
									<div>
										<div className="flex items-center space-x-2">
											<span className="text-sm">Account Center accounts reached</span>
											<Info className="w-4 h-4 text-gray-400" />
										</div>
										<div className="text-xl font-semibold">
											{formatNumber(accountsReached.min)} - {formatNumber(accountsReached.max)}
										</div>
									</div>

									<div>
										<div className="flex items-center space-x-2">
											<span className="text-sm">Page Likes</span>
											<Info className="w-4 h-4 text-gray-400" />
										</div>
										<div className="text-xl font-semibold">
											{pageLikes.min} - {pageLikes.max}
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					{/* Action Buttons */}
					<div className="flex justify-end space-x-3 pt-6 border-t">
						<Button variant="outline" onClick={onClose}>
							Maybe later
						</Button>
						<Button
							className="bg-blue-600 hover:bg-blue-700"
							disabled={isLoading}
							onClick={async () => {
								setIsLoading(true);
								const adData = {
									id,
									postText,
									selectedImages,
									selectedAudience,
									boostEnabled,
									logo,
									goal: adGoal,
									budget: dailyBudget,
									pageLikes,
									accountsReached,
									minBudget,
									maxBudget,
								};

								const stored = localStorage.getItem('ads');
								let ads = stored ? JSON.parse(stored) : [];

								const index = ads.findIndex((ad: any) => ad.id === id);
								if (index !== -1) {
									ads[index] = adData;
								} else {
									ads.push(adData);
								}
								localStorage.setItem('ads', JSON.stringify(ads));
								await new Promise((res) => setTimeout(res, 3000));
								onClose();
								setIsLoading(false);
								onGoToAdDetail(id);
							}}
						>
							Create ad
						</Button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default PublishSuccessModal;