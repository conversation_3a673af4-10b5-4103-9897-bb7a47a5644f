import { format } from 'date-fns';
import { dataService } from 'librechat-data-provider';
import { Loader } from 'lucide-react';
import { useEffect, useState } from 'react';
import { DateRange } from 'react-day-picker';
import { Bar, BarChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import { DateRangePicker, Dropdown } from '~/components/ui';

type ActiveRange = 'yesterday' | 'day' | 'week' | 'month' | 'custom';
type TrendingType = 'user' | 'completion';

const listColors = ['#3B82F6', '#A855F7', '#10B981', '#F59E0B', '#EF4444'];

const ActiveRangeSelector = ({
  value,
  onChange,
  isLoading,
}: {
  value: ActiveRange;
  onChange: (value: ActiveRange) => void;
  isLoading?: boolean;
}) => {
  const handleChange = (val: string) => {
    onChange(val as ActiveRange);
  };

  const options = [
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'today', label: 'Today' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
    { value: 'custom', label: 'Custom' },
  ];

  return (
    <Dropdown
      value={value}
      options={options}
      onChange={handleChange}
      testId="font-size-selector"
      sizeClasses="w-[150px]"
      selectorClassName="h-10"
      selectorProps={{ disabled: isLoading }}
      icon={isLoading && <Loader className="h-4 w-4" />}
    />
  );
};

const TrendingTypeSelector = ({
  value,
  onChange,
  isLoading,
}: {
  value: TrendingType;
  onChange: (value: TrendingType) => void;
  isLoading?: boolean;
}) => {
  const handleChange = (val: string) => {
    onChange(val as TrendingType);
  };

  const options = [
    { value: 'user', label: 'User Participation' },
    { value: 'completion', label: 'User Completion' },
  ];

  return (
    <Dropdown
      value={value}
      options={options}
      onChange={handleChange}
      testId="font-size-selector"
      sizeClasses="w-[200px]"
      selectorClassName="h-10"
      selectorProps={{ disabled: isLoading }}
      icon={isLoading && <Loader className="h-4 w-4" />}
    />
  );
};

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && !!payload?.length) {
    return (
      <div className="min-w-[200px] rounded-lg bg-white p-4 drop-shadow-lg">
        <p className="mb-2 font-medium text-gray-900">{label}</p>
        <div className="flex flex-col space-y-1">
          {payload.map((item: any) => (
            <div key={item.dataKey} className="flex">
              <div className="mr-2 h-3 w-3 rounded-full" style={{ backgroundColor: item.fill }} />
              <span className="text-sm text-gray-600">
                {item.name}: {item.value.toLocaleString()}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  }
  return null;
};

const getChartDataAsync = async (params: any): Promise<{ targets: any[]; data: any[] }> => {
  const result = await dataService.getEmployerDashboardTopPerformingJobs(
    Object.assign(params || {}, { limit: 5 }),
  );
  const targets = (result?.data?.[0]?.items || []).map((item: any, index: number) => ({
    name: item.name,
    value: item.value,
    id: item.jobSimulationId,
    color: listColors[index] || '#3B82F6',
  }));

  const chartData = (result?.data || []).map((item: any) => {
    return {
      labelX: item.time,
      ...item.items.reduce((acc: any, cur: any) => {
        acc[cur.jobSimulationId] = cur.value;
        return acc;
      }, {}),
    };
  });

  return {
    targets,
    data: chartData,
  };
};

const defaultRangeType = 'week';

const TopPerformingJobSimsChart = () => {
  const [trendingType, setTrendingType] = useState<TrendingType>('user');
  const [dateRange, setDateRange] = useState<{ dateFrom?: string; dateTo?: string } | undefined>(
    undefined,
  );
  const [isGettingData, setIsGettingData] = useState(false);
  const [targets, setTargets] = useState<
    { name: string; value: number; id: string; color: string }[]
  >([]);

  const [chartData, setChartData] = useState<{ labelX: string; [key: string]: any }[]>([]);

  const getChartData = async () => {
    if (isGettingData) return;

    setIsGettingData(true);
    const result = await getChartDataAsync({
      timeType: dateRange?.dateFrom && dateRange?.dateTo ? 'custom' : defaultRangeType,
      trendingType,
      limit: 5,
      ...dateRange,
    });

    setTargets(result.targets);
    setChartData(result.data);
    setIsGettingData(false);
  };

  const handleSelectDateRange = (dates: DateRange) => {
    // TODO: Question: What timezone will be used (???). User's timezone or UTC?
    setDateRange({
      dateFrom: format(dates.from!, 'yyyy-MM-dd'),
      dateTo: format(dates.to!, 'yyyy-MM-dd'),
    });
  };

  useEffect(() => {
    getChartData();
  }, [trendingType, dateRange]);

  return (
    <div className="rounded-xl bg-white p-6 text-[#505050] drop-shadow-lg">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="min-w-52 text-xl font-semibold">Top Performing Job Simulations</h3>
        <div className="flex flex-1 flex-wrap justify-end space-x-0 space-y-2 md:space-x-2 md:space-y-0">
          <TrendingTypeSelector
            value={trendingType}
            onChange={setTrendingType}
            isLoading={isGettingData}
          />
          <DateRangePicker
            onConfirm={handleSelectDateRange}
            disabled={isGettingData}
            defaultRange={defaultRangeType}
          />
        </div>
      </div>
      <hr />
      <div className="my-4 flex justify-end space-x-4 min-h-6">
        {targets.map((target) => (
          <div key={target.id} className="flex flex-wrap items-center">
            <div
              className="mr-2 h-3 w-3 rounded-full"
              style={{ backgroundColor: target.color }}
            ></div>
            <span className="text-sm text-gray-600">{target.name}</span>
          </div>
        ))}
      </div>

      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={chartData} barCategoryGap="20%">
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis
              dataKey="labelX"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6B7280' }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6B7280' }}
              allowDecimals={false}
              type="number"
              //   domain={[0, 4000]}
              tickFormatter={(value) =>
                value >= 1000
                  ? `${value / 1000}k ${value > 9 ? 'Users' : 'User'}`
                  : `${value} ${value > 9 ? 'Users' : 'User'}`
              }
            />
            <Tooltip content={<CustomTooltip />} cursor={false} />
            {targets.map((target) => (
              <Bar
                key={target.id}
                dataKey={target.id}
                fill={target.color}
                radius={[4, 4, 0, 0]}
                maxBarSize={40}
                name={target.name || target.id}
              />
            ))}
            {/* <Bar dataKey="esgAnalyst" fill="#3B82F6" radius={[4, 4, 0, 0]} maxBarSize={40} />
            <Bar dataKey="digitalMarketing" fill="#A855F7" radius={[4, 4, 0, 0]} maxBarSize={40} /> */}
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default TopPerformingJobSimsChart;
